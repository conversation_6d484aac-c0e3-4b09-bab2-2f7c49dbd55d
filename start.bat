@echo off
chcp 65001 >nul
echo 🚀 启动高考数据管理平台...
echo.

echo 📋 检查环境要求...

where python >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python 未安装或未添加到PATH
    pause
    exit /b 1
)

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或未添加到PATH
    pause
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm 未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

echo 🔧 启动后端服务...
start "后端服务" cmd /k "cd backend && if not exist venv python -m venv venv && call venv\Scripts\activate.bat && pip install -r requirements.txt && if not exist .env copy .env.example .env && python run.py"

echo 等待后端启动...
timeout /t 10 /nobreak >nul

echo 🎨 启动前端服务...
start "前端服务" cmd /k "cd frontend && if not exist node_modules npm install && npm run dev"

echo.
echo 🎉 高考数据管理平台启动成功！
echo.
echo 📱 前端地址: http://localhost:3000
echo 🔧 后端地址: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo.
echo 👤 默认管理员账号: admin / admin123
echo.
echo 按任意键关闭此窗口...
pause >nul
