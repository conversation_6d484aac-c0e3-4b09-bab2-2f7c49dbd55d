#!/bin/bash

# 高考数据管理平台启动脚本

echo "🚀 启动高考数据管理平台..."

# 检查是否安装了必要的工具
check_requirements() {
    echo "📋 检查环境要求..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3.8+ 未安装"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 16+ 未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装"
        exit 1
    fi
    
    echo "✅ 环境检查通过"
}

# 启动后端服务
start_backend() {
    echo "🔧 启动后端服务..."
    
    cd backend
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        echo "📦 创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    echo "📦 安装Python依赖..."
    pip install -r requirements.txt
    
    # 检查环境配置文件
    if [ ! -f ".env" ]; then
        echo "📝 创建环境配置文件..."
        cp .env.example .env
        echo "⚠️  请编辑 backend/.env 文件配置数据库和其他参数"
    fi
    
    # 初始化数据库
    echo "🗄️  初始化数据库..."
    if [ ! -d "migrations" ]; then
        aerich init -t app.core.database.TORTOISE_ORM
        aerich init-db
    else
        aerich upgrade
    fi
    
    # 启动后端服务
    echo "🚀 启动后端服务 (http://localhost:8000)..."
    python run.py &
    BACKEND_PID=$!
    
    cd ..
}

# 启动前端服务
start_frontend() {
    echo "🎨 启动前端服务..."
    
    cd frontend
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo "📦 安装前端依赖..."
        npm install
    fi
    
    # 启动前端服务
    echo "🚀 启动前端服务 (http://localhost:3000)..."
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
}

# 清理函数
cleanup() {
    echo "🛑 正在停止服务..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
    fi
    echo "✅ 服务已停止"
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    check_requirements
    start_backend
    sleep 5  # 等待后端启动
    start_frontend
    
    echo ""
    echo "🎉 高考数据管理平台启动成功！"
    echo ""
    echo "📱 前端地址: http://localhost:3000"
    echo "🔧 后端地址: http://localhost:8000"
    echo "📚 API文档: http://localhost:8000/docs"
    echo ""
    echo "👤 默认管理员账号: admin / admin123"
    echo ""
    echo "按 Ctrl+C 停止服务"
    
    # 等待进程
    wait
}

# 执行主函数
main
