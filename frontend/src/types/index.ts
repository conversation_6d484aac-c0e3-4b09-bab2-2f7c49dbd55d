// 导出所有类型定义
export * from './auth'

// 通用响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  code?: number
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 通用列表查询参数
export interface ListQuery {
  page?: number
  size?: number
  search?: string
  sort?: string
  order?: 'asc' | 'desc'
}

// 菜单路由类型
export interface RouteMetaType {
  title?: string
  icon?: string
  requiresAuth?: boolean
  permissions?: string[]
  order?: number
}

// 系统配置类型
export interface SystemConfig {
  id: number
  key: string
  value: string
  data_type: string
  category: string
  description?: string
  is_public: boolean
  is_editable: boolean
  created_at: string
  updated_at: string
}

// 大学类型
export interface University {
  id: number
  code: string
  name: string
  english_name?: string
  province: string
  city?: string
  is_985: boolean
  is_211: boolean
  is_double_first_class: boolean
  school_type?: string
  school_nature?: string
  total_majors: number
  ranking_national?: number
  ranking_provincial?: number
  website?: string
  address?: string
  created_at: string
  updated_at: string
}

// 专业类型
export interface Major {
  id: number
  code?: string
  name: string
  category?: string
  degree_type?: string
  duration: number
  popularity_score: number
  employment_rate?: number
  average_salary?: number
  total_enrollments: number
  total_universities: number
  description?: string
  employment_prospects?: string
  created_at: string
  updated_at: string
}

// 录取记录类型
export interface AdmissionRecord {
  id: number
  university_id: number
  major_id: number
  year: number
  province: string
  batch: string
  science_type: string
  min_score: number
  max_score: number
  avg_score: number
  min_rank: number
  max_rank: number
  enrollment_count: number
  created_at: string
  updated_at: string
}

// 推荐配置类型
export interface RecommendationConfig {
  id: number
  user_id: number
  config_name: string
  target_year: number
  science_type: string
  score_weight: number
  rank_weight: number
  school_level_weight: number
  location_weight: number
  major_preference_weight: number
  rush_ratio: number
  stable_ratio: number
  safe_ratio: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// 推荐结果类型
export interface RecommendationResult {
  id: number
  config_id: number
  university_id: number
  major_id: number
  recommendation_type: 'rush' | 'stable' | 'safe'
  total_score: number
  score_match_score: number
  rank_match_score: number
  school_level_score: number
  location_score: number
  major_preference_score: number
  overall_rank: number
  is_favorited: boolean
  created_at: string
  updated_at: string
}

// 爬虫任务类型
export interface CrawlerTask {
  id: number
  task_name: string
  data_type: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused'
  total_records: number
  processed_records: number
  progress_percentage: number
  error_message?: string
  created_at: string
  updated_at: string
  completed_at?: string
}

// 爬虫日志类型
export interface CrawlerLog {
  id: number
  task_id: number
  level: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR'
  message: string
  created_at: string
}
