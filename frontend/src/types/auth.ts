export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  role: string
  is_active: boolean
  is_verified: boolean
  last_login?: string
  created_at: string
}

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  full_name?: string
}

export interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

export interface PasswordChangeForm {
  old_password: string
  new_password: string
}

export interface UserPermissions {
  permissions: string[]
  role: string
}
