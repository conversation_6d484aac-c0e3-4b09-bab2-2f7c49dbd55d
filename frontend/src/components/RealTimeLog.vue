<template>
  <div class="real-time-log">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>📝 实时日志</span>
          <div class="header-actions">
            <el-select 
              v-model="logLevel" 
              placeholder="日志级别"
              size="small"
              style="width: 120px; margin-right: 8px;"
            >
              <el-option label="全部" value="" />
              <el-option label="DEBUG" value="DEBUG" />
              <el-option label="INFO" value="INFO" />
              <el-option label="WARNING" value="WARNING" />
              <el-option label="ERROR" value="ERROR" />
            </el-select>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索日志"
              size="small"
              style="width: 200px; margin-right: 8px;"
              :prefix-icon="Search"
              clearable
            />
            <el-button 
              :icon="connected ? Pause : VideoPlay" 
              @click="toggleConnection"
              :type="connected ? 'warning' : 'success'"
              size="small"
            >
              {{ connected ? '暂停' : '开始' }}
            </el-button>
            <el-button 
              :icon="Delete" 
              @click="clearLogs"
              size="small"
            >
              清空
            </el-button>
          </div>
        </div>
      </template>

      <!-- 连接状态 -->
      <div class="connection-status">
        <el-tag :type="connected ? 'success' : 'danger'" size="small">
          <el-icon><component :is="connected ? 'Connection' : 'Close'" /></el-icon>
          {{ connected ? '已连接' : '未连接' }}
        </el-tag>
        <span class="log-count">共 {{ filteredLogs.length }} 条日志</span>
      </div>

      <!-- 日志内容 -->
      <div class="log-container" ref="logContainer">
        <div 
          v-for="(log, index) in filteredLogs" 
          :key="index"
          :class="['log-item', `log-${log.level.toLowerCase()}`]"
        >
          <div class="log-header">
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <el-tag 
              :type="getLogLevelType(log.level)" 
              size="small"
              class="log-level"
            >
              {{ log.level }}
            </el-tag>
            <span class="log-source">{{ log.source || 'System' }}</span>
          </div>
          <div class="log-message">{{ log.message }}</div>
          <div v-if="log.details" class="log-details">
            <el-collapse>
              <el-collapse-item title="详细信息">
                <pre>{{ log.details }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="empty-logs">
          <el-empty description="暂无日志数据" />
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="log-footer">
        <el-checkbox v-model="autoScroll">自动滚动到底部</el-checkbox>
        <span class="last-update">
          最后更新: {{ lastUpdateTime }}
        </span>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Delete, 
  VideoPlay, 
  Pause, 
  Connection, 
  Close 
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const connected = ref(false)
const logLevel = ref('')
const searchKeyword = ref('')
const autoScroll = ref(true)
const lastUpdateTime = ref('')
const logContainer = ref<HTMLElement>()

const logs = ref<Array<{
  timestamp: string
  level: string
  message: string
  source?: string
  details?: string
}>>([])

// WebSocket连接
let ws: WebSocket | null = null

// 计算属性
const filteredLogs = computed(() => {
  let filtered = logs.value

  // 按日志级别过滤
  if (logLevel.value) {
    filtered = filtered.filter(log => log.level === logLevel.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(log => 
      log.message.toLowerCase().includes(keyword) ||
      log.source?.toLowerCase().includes(keyword)
    )
  }

  return filtered.slice(-1000) // 只显示最近1000条
})

// 监听日志变化，自动滚动
watch(filteredLogs, () => {
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
})

// 方法
const getLogLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    DEBUG: 'info',
    INFO: 'success',
    WARNING: 'warning',
    ERROR: 'danger'
  }
  return typeMap[level] || 'info'
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('HH:mm:ss.SSS')
}

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight
  }
}

const connectWebSocket = () => {
  try {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}/api/ws/logs`
    
    ws = new WebSocket(wsUrl)
    
    ws.onopen = () => {
      connected.value = true
      ElMessage.success('日志连接已建立')
    }
    
    ws.onmessage = (event) => {
      try {
        const logData = JSON.parse(event.data)
        logs.value.push({
          timestamp: logData.timestamp || new Date().toISOString(),
          level: logData.level || 'INFO',
          message: logData.message || '',
          source: logData.source,
          details: logData.details
        })
        lastUpdateTime.value = dayjs().format('HH:mm:ss')
      } catch (error) {
        console.error('解析日志数据失败:', error)
      }
    }
    
    ws.onclose = () => {
      connected.value = false
      ElMessage.warning('日志连接已断开')
    }
    
    ws.onerror = () => {
      connected.value = false
      ElMessage.error('日志连接出错')
    }
  } catch (error) {
    ElMessage.error('无法建立日志连接')
  }
}

const disconnectWebSocket = () => {
  if (ws) {
    ws.close()
    ws = null
  }
  connected.value = false
}

const toggleConnection = () => {
  if (connected.value) {
    disconnectWebSocket()
  } else {
    connectWebSocket()
  }
}

const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清空')
}

// 模拟日志数据（用于演示）
const generateMockLogs = () => {
  const levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
  const sources = ['Crawler', 'Database', 'API', 'System']
  const messages = [
    '开始爬取学校数据',
    '成功获取学校列表',
    '处理专业数据中...',
    '保存数据到数据库',
    '网络请求超时，正在重试',
    '数据验证完成',
    '任务执行完成'
  ]

  setInterval(() => {
    if (connected.value && logs.value.length < 100) {
      const log = {
        timestamp: new Date().toISOString(),
        level: levels[Math.floor(Math.random() * levels.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        source: sources[Math.floor(Math.random() * sources.length)]
      }
      logs.value.push(log)
      lastUpdateTime.value = dayjs().format('HH:mm:ss')
    }
  }, 2000)
}

// 生命周期
onMounted(() => {
  // 尝试连接WebSocket
  connectWebSocket()
  
  // 如果WebSocket连接失败，使用模拟数据
  setTimeout(() => {
    if (!connected.value) {
      connected.value = true
      generateMockLogs()
    }
  }, 2000)
})

onUnmounted(() => {
  disconnectWebSocket()
})
</script>

<style lang="scss" scoped>
.real-time-log {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    align-items: center;
  }

  .connection-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: #f5f7fa;
    border-radius: 4px;

    .log-count {
      font-size: 12px;
      color: #909399;
    }
  }

  .log-container {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px;
    background: #fafafa;

    .log-item {
      margin-bottom: 8px;
      padding: 8px;
      border-radius: 4px;
      background: white;
      border-left: 3px solid #e4e7ed;

      &.log-debug {
        border-left-color: #909399;
      }

      &.log-info {
        border-left-color: #67c23a;
      }

      &.log-warning {
        border-left-color: #e6a23c;
      }

      &.log-error {
        border-left-color: #f56c6c;
      }

      .log-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;

        .log-time {
          font-size: 12px;
          color: #909399;
          font-family: monospace;
        }

        .log-level {
          font-size: 10px;
        }

        .log-source {
          font-size: 12px;
          color: #606266;
        }
      }

      .log-message {
        font-size: 14px;
        color: #303133;
        line-height: 1.4;
      }

      .log-details {
        margin-top: 8px;

        pre {
          font-size: 12px;
          color: #606266;
          background: #f5f7fa;
          padding: 8px;
          border-radius: 4px;
          overflow-x: auto;
        }
      }
    }

    .empty-logs {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }
  }

  .log-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;

    .last-update {
      font-size: 12px;
      color: #909399;
    }
  }
}
</style>
