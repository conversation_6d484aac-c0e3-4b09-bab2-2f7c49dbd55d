<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建爬取任务"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="任务名称" prop="task_name">
        <el-input 
          v-model="form.task_name" 
          placeholder="请输入任务名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="爬取年份" prop="years">
        <el-select 
          v-model="form.years" 
          multiple 
          placeholder="选择要爬取的年份"
          style="width: 100%"
        >
          <el-option label="2024年" value="2024" />
          <el-option label="2023年" value="2023" />
          <el-option label="2022年" value="2022" />
          <el-option label="2021年" value="2021" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="科类" prop="science_types">
        <el-select 
          v-model="form.science_types" 
          multiple 
          placeholder="选择科类"
          style="width: 100%"
        >
          <el-option label="物理类" value="physics" />
          <el-option label="历史类" value="history" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="任务描述">
        <el-input 
          v-model="form.description" 
          type="textarea" 
          :rows="3"
          placeholder="请输入任务描述（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 高级选项 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="高级选项" name="advanced">
          <el-form-item label="并发数量">
            <el-input-number 
              v-model="form.max_concurrent" 
              :min="1" 
              :max="20"
              placeholder="并发请求数量"
            />
            <el-text type="info" size="small" style="margin-left: 8px;">
              建议值: 5-10
            </el-text>
          </el-form-item>
          
          <el-form-item label="请求间隔">
            <el-input-number 
              v-model="form.request_delay" 
              :min="0.1" 
              :max="10"
              :step="0.1"
              placeholder="请求间隔（秒）"
            />
            <el-text type="info" size="small" style="margin-left: 8px;">
              建议值: 0.5-2.0秒
            </el-text>
          </el-form-item>
          
          <el-form-item label="重试次数">
            <el-input-number 
              v-model="form.max_retries" 
              :min="1" 
              :max="10"
              placeholder="失败重试次数"
            />
          </el-form-item>
        </el-collapse-item>
      </el-collapse>

      <!-- 预估信息 -->
      <el-alert
        v-if="estimatedInfo"
        :title="estimatedInfo"
        type="info"
        :closable="false"
        style="margin-top: 16px;"
      />
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
        >
          创建任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

// Props & Emits
const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const activeCollapse = ref<string[]>([])

const form = ref({
  task_name: '',
  years: ['2024'],
  science_types: ['physics', 'history'],
  description: '',
  max_concurrent: 5,
  request_delay: 1.0,
  max_retries: 3
})

// 表单验证规则
const rules: FormRules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '任务名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  years: [
    { required: true, message: '请选择要爬取的年份', trigger: 'change' }
  ],
  science_types: [
    { required: true, message: '请选择科类', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const estimatedInfo = computed(() => {
  if (form.value.years.length === 0 || form.value.science_types.length === 0) {
    return ''
  }
  
  const yearCount = form.value.years.length
  const scienceCount = form.value.science_types.length
  const estimatedSchools = 2800 // 预估学校数量
  const estimatedTime = Math.ceil((yearCount * scienceCount * estimatedSchools) / (form.value.max_concurrent * 60 / form.value.request_delay))
  
  return `预估爬取 ${yearCount} 个年份 × ${scienceCount} 个科类 × ${estimatedSchools} 所学校，大约需要 ${estimatedTime} 分钟`
})

// 监听对话框状态
watch(dialogVisible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 方法
const resetForm = () => {
  form.value = {
    task_name: '',
    years: ['2024'],
    science_types: ['physics', 'history'],
    description: '',
    max_concurrent: 5,
    request_delay: 1.0,
    max_retries: 3
  }
  activeCollapse.value = []
  formRef.value?.clearValidate()
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  loading.value = true
  try {
    // 这里应该调用API创建任务
    // await crawlerApi.createTask(form.value)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('任务创建成功')
    emit('success')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('创建任务失败')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
