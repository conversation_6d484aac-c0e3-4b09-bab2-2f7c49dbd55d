<template>
  <div class="crawler-task-manager">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>📋 任务管理</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              :icon="Plus" 
              @click="showCreateDialog = true"
              :disabled="hasRunningTask"
            >
              创建任务
            </el-button>
            <el-button 
              :icon="Refresh" 
              @click="loadTasks" 
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 任务列表 -->
      <el-table 
        :data="tasks" 
        v-loading="loading"
        stripe
        style="width: 100%"
        empty-text="暂无任务"
      >
        <el-table-column prop="id" label="任务ID" width="100" />
        <el-table-column prop="task_name" label="任务名称" min-width="150" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进度" width="200">
          <template #default="{ row }">
            <div class="progress-container">
              <el-progress 
                :percentage="row.progress_percentage || 0" 
                :status="getProgressStatus(row.status)"
                :stroke-width="8"
              />
              <span class="progress-text">
                {{ row.processed_records || 0 }}/{{ row.total_records || 0 }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                v-if="row.status === 'running'"
                size="small" 
                type="warning"
                @click="controlTask(row.id, 'pause')"
              >
                暂停
              </el-button>
              <el-button 
                v-if="row.status === 'paused'"
                size="small" 
                type="success"
                @click="controlTask(row.id, 'resume')"
              >
                恢复
              </el-button>
              <el-button 
                v-if="['running', 'paused'].includes(row.status)"
                size="small" 
                type="danger"
                @click="controlTask(row.id, 'cancel')"
              >
                取消
              </el-button>
              <el-button 
                size="small" 
                @click="viewTaskDetail(row)"
              >
                详情
              </el-button>
              <el-button 
                v-if="['completed', 'failed', 'cancelled'].includes(row.status)"
                size="small" 
                type="danger"
                @click="deleteTask(row.id)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建任务对话框 -->
    <CreateTaskDialog
      v-model="showCreateDialog"
      @success="handleTaskCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, View, VideoPause, VideoPlay, Delete } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import CreateTaskDialog from './CreateTaskDialog.vue'
// import TaskDetailDialog from './TaskDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedTask = ref(null)

const tasks = ref([
  {
    id: 1,
    task_name: '2024年高考数据爬取',
    status: 'running',
    progress_percentage: 65,
    processed_records: 1300,
    total_records: 2000,
    created_at: '2024-06-20 10:30:00',
    data_type: '高校专业数据'
  },
  {
    id: 2,
    task_name: '2023年历史数据补充',
    status: 'completed',
    progress_percentage: 100,
    processed_records: 1856,
    total_records: 1856,
    created_at: '2024-06-19 14:20:00',
    data_type: '高校专业数据'
  },
  {
    id: 3,
    task_name: '2022年数据验证',
    status: 'failed',
    progress_percentage: 45,
    processed_records: 900,
    total_records: 2000,
    created_at: '2024-06-18 09:15:00',
    data_type: '高校专业数据'
  }
])

// 计算属性
const hasRunningTask = computed(() => {
  return tasks.value.some(task => ['running', 'pending'].includes(task.status))
})

// 方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    paused: 'info',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '等待中',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return textMap[status] || status
}

const getProgressStatus = (status: string) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const loadTasks = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取任务列表
    // const response = await crawlerApi.getTasks()
    // tasks.value = response.data
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const controlTask = async (taskId: number, action: string) => {
  const actionText = {
    pause: '暂停',
    resume: '恢复',
    cancel: '取消'
  }[action] || action

  try {
    await ElMessageBox.confirm(
      `确定要${actionText}此任务吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API控制任务
    // await crawlerApi.controlTask(taskId, { action })
    
    ElMessage.success(`任务${actionText}成功`)
    await loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`任务${actionText}失败`)
    }
  }
}

const deleteTask = async (taskId: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除此任务吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API删除任务
    // await crawlerApi.deleteTask(taskId)
    
    ElMessage.success('任务删除成功')
    await loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('任务删除失败')
    }
  }
}

const viewTaskDetail = (task: any) => {
  selectedTask.value = task
  showDetailDialog.value = true
}

const handleTaskCreated = () => {
  loadTasks()
}

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style lang="scss" scoped>
.crawler-task-manager {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .progress-container {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .progress-text {
      font-size: 12px;
      color: #909399;
      text-align: center;
    }
  }

  .action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }
}
</style>
