<template>
  <div class="system-status">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>🖥️ 系统状态</span>
          <el-button 
            :icon="Refresh" 
            @click="refreshStatus" 
            :loading="loading"
            size="small"
          >
            刷新
          </el-button>
        </div>
      </template>
      
      <div class="status-grid">
        <!-- 服务状态 -->
        <div class="status-section">
          <h4>服务状态</h4>
          <div class="status-items">
            <div class="status-item">
              <span class="label">后端服务</span>
              <el-tag :type="status.backend ? 'success' : 'danger'">
                {{ status.backend ? '正常' : '异常' }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="label">数据库</span>
              <el-tag :type="status.database === 'ok' ? 'success' : 'danger'">
                {{ status.database === 'ok' ? '正常' : '异常' }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="label">Redis缓存</span>
              <el-tag :type="status.redis === 'ok' ? 'success' : 'warning'">
                {{ status.redis === 'ok' ? '正常' : '未连接' }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 系统资源 -->
        <div class="status-section">
          <h4>系统资源</h4>
          <div class="resource-items">
            <div class="resource-item">
              <span class="label">CPU使用率</span>
              <el-progress 
                :percentage="status.system?.cpu_percent || 0" 
                :stroke-width="8"
                :color="getProgressColor(status.system?.cpu_percent || 0)"
              />
            </div>
            <div class="resource-item">
              <span class="label">内存使用率</span>
              <el-progress 
                :percentage="status.system?.memory_percent || 0" 
                :stroke-width="8"
                :color="getProgressColor(status.system?.memory_percent || 0)"
              />
            </div>
          </div>
        </div>

        <!-- 任务统计 -->
        <div class="status-section">
          <h4>任务统计</h4>
          <div class="stats-items">
            <div class="stat-item">
              <div class="stat-value">{{ status.tasks?.total || 0 }}</div>
              <div class="stat-label">总任务数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ status.tasks?.running || 0 }}</div>
              <div class="stat-label">运行中</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ status.tasks?.completed || 0 }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ status.tasks?.failed || 0 }}</div>
              <div class="stat-label">失败</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最后更新时间 -->
      <div class="update-time">
        <el-text type="info" size="small">
          最后更新: {{ lastUpdateTime }}
        </el-text>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const lastUpdateTime = ref('')

const status = ref({
  backend: true,
  database: 'ok',
  redis: 'ok',
  system: {
    cpu_percent: 0,
    memory_percent: 0
  },
  tasks: {
    total: 0,
    running: 0,
    completed: 0,
    failed: 0
  }
})

// 定时器
let statusInterval: number

// 方法
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const refreshStatus = async () => {
  loading.value = true
  try {
    // 测试后端连接
    const response = await fetch('/api/health')
    if (response.ok) {
      const data = await response.json()
      status.value.backend = true
      status.value.database = data.database || 'ok'
      status.value.redis = data.redis || 'disconnected'
    } else {
      status.value.backend = false
    }

    // 模拟系统资源数据
    status.value.system = {
      cpu_percent: Math.floor(Math.random() * 100),
      memory_percent: Math.floor(Math.random() * 100)
    }

    // 模拟任务统计数据
    status.value.tasks = {
      total: Math.floor(Math.random() * 50) + 10,
      running: Math.floor(Math.random() * 5),
      completed: Math.floor(Math.random() * 40) + 5,
      failed: Math.floor(Math.random() * 3)
    }

    lastUpdateTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
  } catch (error) {
    console.error('获取系统状态失败:', error)
    status.value.backend = false
    ElMessage.error('获取系统状态失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshStatus()
  // 每30秒自动刷新
  statusInterval = setInterval(refreshStatus, 30000)
})

onUnmounted(() => {
  if (statusInterval) {
    clearInterval(statusInterval)
  }
})
</script>

<style lang="scss" scoped>
.system-status {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
  }

  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 20px;
  }

  .status-section {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .status-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    .label {
      color: #606266;
      font-size: 14px;
    }
  }

  .resource-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .resource-item {
    .label {
      display: block;
      margin-bottom: 8px;
      color: #606266;
      font-size: 14px;
    }
  }

  .stats-items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .stat-item {
    text-align: center;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 8px;

    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #409eff;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #909399;
    }
  }

  .update-time {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
