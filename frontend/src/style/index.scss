// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 工具类
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// 间距工具类
@for $i from 0 through 50 {
  .m-#{$i} { margin: #{$i}px !important; }
  .mt-#{$i} { margin-top: #{$i}px !important; }
  .mr-#{$i} { margin-right: #{$i}px !important; }
  .mb-#{$i} { margin-bottom: #{$i}px !important; }
  .ml-#{$i} { margin-left: #{$i}px !important; }
  .mx-#{$i} { margin-left: #{$i}px !important; margin-right: #{$i}px !important; }
  .my-#{$i} { margin-top: #{$i}px !important; margin-bottom: #{$i}px !important; }
  
  .p-#{$i} { padding: #{$i}px !important; }
  .pt-#{$i} { padding-top: #{$i}px !important; }
  .pr-#{$i} { padding-right: #{$i}px !important; }
  .pb-#{$i} { padding-bottom: #{$i}px !important; }
  .pl-#{$i} { padding-left: #{$i}px !important; }
  .px-#{$i} { padding-left: #{$i}px !important; padding-right: #{$i}px !important; }
  .py-#{$i} { padding-top: #{$i}px !important; padding-bottom: #{$i}px !important; }
}

// 页面容器
.page-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.card-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 表单样式增强
.el-form {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

// 按钮样式增强
.el-button {
  &.is-circle {
    padding: 8px;
  }
}

// 卡片样式增强
.el-card {
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    border-bottom: 1px solid #ebeef5;
    padding: 18px 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 对话框样式增强
.el-dialog {
  .el-dialog__header {
    border-bottom: 1px solid #ebeef5;
    padding: 20px 20px 15px;
    
    .el-dialog__title {
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    border-top: 1px solid #ebeef5;
    padding: 15px 20px 20px;
  }
}

// 抽屉样式增强
.el-drawer {
  .el-drawer__header {
    border-bottom: 1px solid #ebeef5;
    padding: 20px;
    margin-bottom: 0;
    
    .el-drawer__title {
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-drawer__body {
    padding: 20px;
  }
}

// 标签页样式增强
.el-tabs {
  .el-tabs__header {
    margin-bottom: 20px;
  }
  
  .el-tabs__item {
    font-weight: 500;
    
    &.is-active {
      color: #409eff;
    }
  }
}

// 分页样式增强
.el-pagination {
  margin-top: 20px;
  text-align: center;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  
  .loading-text {
    margin-left: 10px;
    color: #909399;
  }
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 14px;
  }
}

// 状态标签
.status-tag {
  &.success {
    background-color: #f0f9ff;
    color: #67c23a;
    border: 1px solid #b3e19d;
  }
  
  &.warning {
    background-color: #fdf6ec;
    color: #e6a23c;
    border: 1px solid #f5dab1;
  }
  
  &.danger {
    background-color: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fbc4c4;
  }
  
  &.info {
    background-color: #f4f4f5;
    color: #909399;
    border: 1px solid #d3d4d6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .card-container {
    padding: 15px;
    margin-bottom: 15px;
  }
  
  .el-card .el-card__header,
  .el-card .el-card__body {
    padding: 15px;
  }
}

// 暗色主题
.dark {
  background-color: #1d1e1f;
  color: #e5eaf3;
  
  .el-card {
    background-color: #2b2f3a;
    border-color: #414243;
    color: #e5eaf3;
    
    .el-card__header {
      border-color: #414243;
      background-color: #363e4f;
    }
  }
  
  .el-table {
    background-color: #2b2f3a;
    color: #e5eaf3;
    
    .el-table__header th {
      background-color: #363e4f;
      color: #e5eaf3;
    }
    
    .el-table__row:hover {
      background-color: #363e4f;
    }
  }
  
  .el-input {
    .el-input__inner {
      background-color: #2b2f3a;
      border-color: #414243;
      color: #e5eaf3;
    }
  }
  
  .el-select {
    .el-input__inner {
      background-color: #2b2f3a;
      border-color: #414243;
      color: #e5eaf3;
    }
  }
}
