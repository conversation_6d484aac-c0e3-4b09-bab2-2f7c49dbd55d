import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '仪表板',
          icon: 'Dashboard',
          requiresAuth: true
        }
      },
      {
        path: '/crawler',
        name: 'Crawler',
        component: () => import('@/views/Crawler.vue'),
        meta: {
          title: '爬虫管理',
          icon: 'Connection',
          requiresAuth: true,
          permissions: ['crawler:view']
        }
      },
      {
        path: '/recommendations',
        name: 'Recommendations',
        component: () => import('@/views/Recommendations.vue'),
        meta: { 
          title: '专业推荐',
          icon: 'Star',
          requiresAuth: true,
          permissions: ['recommendation:view']
        }
      },
      {
        path: '/universities',
        name: 'Universities',
        component: () => import('@/views/Universities.vue'),
        meta: { 
          title: '大学管理',
          icon: 'School',
          requiresAuth: true,
          permissions: ['data:view']
        }
      },
      {
        path: '/majors',
        name: 'Majors',
        component: () => import('@/views/Majors.vue'),
        meta: { 
          title: '专业管理',
          icon: 'Reading',
          requiresAuth: true,
          permissions: ['data:view']
        }
      },
      {
        path: '/analytics',
        name: 'Analytics',
        component: () => import('@/views/Analytics.vue'),
        meta: { 
          title: '数据分析',
          icon: 'TrendCharts',
          requiresAuth: true,
          permissions: ['data:view']
        }
      },
      {
        path: '/users',
        name: 'Users',
        component: () => import('@/views/Users.vue'),
        meta: { 
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          permissions: ['user:view']
        }
      },
      {
        path: '/system',
        name: 'System',
        component: () => import('@/views/System.vue'),
        meta: {
          title: '系统管理',
          icon: 'Setting',
          requiresAuth: true,
          permissions: ['system:config']
        }
      },
      {
        path: '/test',
        name: 'Test',
        component: () => import('@/views/Test.vue'),
        meta: {
          title: '系统测试',
          icon: 'Monitor',
          requiresAuth: false
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      // 尝试从本地存储恢复认证状态
      await authStore.initAuth()
      
      if (!authStore.isAuthenticated) {
        next('/login')
        return
      }
    }
    
    // 检查权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      const hasPermission = to.meta.permissions.some(permission => 
        authStore.hasPermission(permission as string)
      )
      
      if (!hasPermission) {
        ElMessage.error('权限不足')
        next('/dashboard')
        return
      }
    }
  }
  
  // 如果已登录用户访问登录页，重定向到仪表板
  if (to.name === 'Login' && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }
  
  next()
})

export default router
