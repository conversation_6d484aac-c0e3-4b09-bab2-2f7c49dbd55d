import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { User, LoginForm, RegisterForm } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const permissions = ref<string[]>([])
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || '')
  
  // 方法
  const setAuth = (authData: {
    user: User
    access_token: string
    refresh_token: string
    permissions: string[]
  }) => {
    user.value = authData.user
    token.value = authData.access_token
    refreshToken.value = authData.refresh_token
    permissions.value = authData.permissions
    
    // 保存到本地存储
    localStorage.setItem('token', authData.access_token)
    localStorage.setItem('refreshToken', authData.refresh_token)
    localStorage.setItem('user', JSON.stringify(authData.user))
    localStorage.setItem('permissions', JSON.stringify(authData.permissions))
  }
  
  const clearAuth = () => {
    user.value = null
    token.value = ''
    refreshToken.value = ''
    permissions.value = []
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
    localStorage.removeItem('permissions')
  }
  
  const login = async (loginForm: LoginForm) => {
    try {
      const response = await authApi.login(loginForm)
      
      // 获取用户信息和权限
      const userInfo = await authApi.getCurrentUser()
      const userPermissions = await authApi.getUserPermissions()
      
      setAuth({
        user: userInfo,
        access_token: response.access_token,
        refresh_token: response.refresh_token,
        permissions: userPermissions.permissions
      })
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.detail || '登录失败' 
      }
    }
  }
  
  const register = async (registerForm: RegisterForm) => {
    try {
      await authApi.register(registerForm)
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.detail || '注册失败' 
      }
    }
  }
  
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      clearAuth()
    }
  }
  
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新令牌')
      }
      
      const response = await authApi.refreshToken(refreshToken.value)
      
      token.value = response.access_token
      refreshToken.value = response.refresh_token
      
      localStorage.setItem('token', response.access_token)
      localStorage.setItem('refreshToken', response.refresh_token)
      
      return true
    } catch (error) {
      console.error('刷新令牌失败:', error)
      clearAuth()
      return false
    }
  }
  
  const initAuth = async () => {
    const savedToken = localStorage.getItem('token')
    const savedRefreshToken = localStorage.getItem('refreshToken')
    const savedUser = localStorage.getItem('user')
    const savedPermissions = localStorage.getItem('permissions')
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        refreshToken.value = savedRefreshToken || ''
        user.value = JSON.parse(savedUser)
        permissions.value = savedPermissions ? JSON.parse(savedPermissions) : []
        
        // 验证令牌是否有效
        await authApi.getCurrentUser()
      } catch (error) {
        // 令牌无效，尝试刷新
        const refreshed = await refreshAccessToken()
        if (!refreshed) {
          clearAuth()
        }
      }
    }
  }
  
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      const updatedUser = await authApi.updateProfile(profileData)
      user.value = updatedUser
      localStorage.setItem('user', JSON.stringify(updatedUser))
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.detail || '更新失败' 
      }
    }
  }
  
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      await authApi.changePassword({ old_password: oldPassword, new_password: newPassword })
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.detail || '密码修改失败' 
      }
    }
  }
  
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission)
  }
  
  const hasAnyPermission = (permissionList: string[]) => {
    return permissionList.some(permission => permissions.value.includes(permission))
  }
  
  const hasAllPermissions = (permissionList: string[]) => {
    return permissionList.every(permission => permissions.value.includes(permission))
  }
  
  return {
    // 状态
    user,
    token,
    permissions,
    
    // 计算属性
    isAuthenticated,
    userRole,
    
    // 方法
    login,
    register,
    logout,
    refreshAccessToken,
    initAuth,
    updateProfile,
    changePassword,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    clearAuth
  }
})
