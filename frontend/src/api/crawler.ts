import request from '@/utils/request'

// 爬虫任务相关接口
export interface CrawlerTask {
  id: number
  task_name: string
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled'
  progress_percentage: number
  processed_records: number
  total_records: number
  created_at: string
  updated_at: string
  data_type: string
  years: string[]
  science_types: string[]
  description?: string
}

export interface CreateTaskRequest {
  task_name: string
  years: string[]
  science_types: string[]
  description?: string
  max_concurrent?: number
  request_delay?: number
  max_retries?: number
}

export interface TaskControlRequest {
  action: 'pause' | 'resume' | 'cancel' | 'restart'
}

export interface CrawlerLog {
  id: number
  task_id: number
  level: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR'
  message: string
  created_at: string
  source?: string
  details?: string
}

export interface SystemStatus {
  backend: boolean
  database: string
  redis: string
  system?: {
    cpu_percent: number
    memory_percent: number
  }
  tasks?: {
    total: number
    running: number
    completed: number
    failed: number
  }
}

// API接口
export const crawlerApi = {
  // 获取任务列表
  getTasks(params?: {
    limit?: number
    offset?: number
    status_filter?: string
  }) {
    return request.get('/crawler/tasks', { params })
  },

  // 创建任务
  createTask(data: CreateTaskRequest) {
    return request.post('/crawler/tasks', data)
  },

  // 获取任务详情
  getTask(taskId: number) {
    return request.get(`/crawler/tasks/${taskId}`)
  },

  // 控制任务
  controlTask(taskId: number, data: TaskControlRequest) {
    return request.post(`/crawler/tasks/${taskId}/control`, data)
  },

  // 删除任务
  deleteTask(taskId: number) {
    return request.delete(`/crawler/tasks/${taskId}`)
  },

  // 获取任务日志
  getTaskLogs(taskId: number, params?: {
    level?: string
    limit?: number
    offset?: number
  }) {
    return request.get(`/crawler/tasks/${taskId}/logs`, { params })
  },

  // 获取系统状态
  getSystemStatus() {
    return request.get('/system/status')
  },

  // 获取爬虫配置
  getConfig() {
    return request.get('/crawler/config')
  },

  // 更新爬虫配置
  updateConfig(data: any) {
    return request.put('/crawler/config', data)
  },

  // 测试API连接
  testConnection() {
    return request.get('/crawler/test')
  }
}

export default crawlerApi
