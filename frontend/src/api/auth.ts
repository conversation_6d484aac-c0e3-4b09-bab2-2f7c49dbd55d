import request from '@/utils/request'
import type { 
  User, 
  LoginForm, 
  RegisterForm, 
  TokenResponse, 
  PasswordChangeForm,
  UserPermissions 
} from '@/types/auth'

export const authApi = {
  // 用户注册
  register(data: RegisterForm): Promise<User> {
    return request.post('/auth/register', data)
  },

  // 用户登录
  login(data: LoginForm): Promise<TokenResponse> {
    const formData = new FormData()
    formData.append('username', data.username)
    formData.append('password', data.password)
    
    return request.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  },

  // 刷新令牌
  refreshToken(refreshToken: string): Promise<TokenResponse> {
    return request.post('/auth/refresh', { refresh_token: refreshToken })
  },

  // 获取当前用户信息
  getCurrentUser(): Promise<User> {
    return request.get('/auth/me')
  },

  // 更新用户信息
  updateProfile(data: Partial<User>): Promise<User> {
    return request.put('/auth/me', data)
  },

  // 修改密码
  changePassword(data: PasswordChangeForm): Promise<{ message: string }> {
    return request.post('/auth/change-password', data)
  },

  // 用户登出
  logout(): Promise<{ message: string }> {
    return request.post('/auth/logout')
  },

  // 获取用户权限
  getUserPermissions(): Promise<UserPermissions> {
    return request.get('/auth/permissions')
  }
}
