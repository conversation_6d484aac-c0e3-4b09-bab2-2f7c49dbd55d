<template>
  <div class="analytics-page">
    <div class="page-header">
      <h1>数据分析</h1>
      <p>高考录取数据的统计分析和可视化展示</p>
    </div>

    <!-- 概览统计 -->
    <div class="overview-stats mb-20">
      <el-row :gutter="20">
        <el-col :span="6" v-for="stat in overviewStats" :key="stat.title">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                <el-icon :size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-title">{{ stat.title }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>录取分数趋势</span>
              <div class="header-controls">
                <el-select v-model="trendParams.science_type" size="small" style="width: 100px">
                  <el-option label="物理类" value="physics" />
                  <el-option label="历史类" value="history" />
                </el-select>
                <el-button size="small" @click="loadTrendData">刷新</el-button>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <!-- <v-chart :option="trendChartOption" style="height: 300px" /> -->
            <div class="chart-placeholder">图表组件加载中...</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>大学排名分析</span>
              <div class="header-controls">
                <el-select v-model="rankingParams.year" size="small" style="width: 100px">
                  <el-option label="2024" :value="2024" />
                  <el-option label="2023" :value="2023" />
                  <el-option label="2022" :value="2022" />
                </el-select>
                <el-button size="small" @click="loadRankingData">刷新</el-button>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <!-- <v-chart :option="rankingChartOption" style="height: 300px" /> -->
            <div class="chart-placeholder">图表组件加载中...</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb-20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>分数分布分析</span>
            </div>
          </template>
          <div class="chart-container">
            <!-- <v-chart :option="distributionChartOption" style="height: 300px" /> -->
            <div class="chart-placeholder">图表组件加载中...</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>省份竞争分析</span>
            </div>
          </template>
          <div class="chart-container">
            <!-- <v-chart :option="competitionChartOption" style="height: 300px" /> -->
            <div class="chart-placeholder">图表组件加载中...</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 专业热度排行 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>专业热度排行榜</span>
          <div class="header-controls">
            <el-select v-model="popularityParams.category" placeholder="专业大类" clearable>
              <el-option label="工学" value="工学" />
              <el-option label="理学" value="理学" />
              <el-option label="经济学" value="经济学" />
              <el-option label="管理学" value="管理学" />
            </el-select>
            <el-button size="small" @click="loadPopularityData">刷新</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="popularityRanking" stripe>
        <el-table-column prop="rank" label="排名" width="80" />
        <el-table-column prop="name" label="专业名称" width="200" />
        <el-table-column prop="category" label="专业大类" width="120" />
        <el-table-column label="热度分数" width="150">
          <template #default="{ row }">
            <el-progress 
              :percentage="Math.min(row.value, 100)" 
              :stroke-width="8"
              :show-text="false"
            />
            <span class="score-text">{{ row.value.toFixed(1) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="录取人数" width="120" />
        <el-table-column label="就业信息" width="200">
          <template #default="{ row }">
            <div v-if="row.extra_info">
              <div>就业率: {{ row.extra_info.employment_rate ? (row.extra_info.employment_rate * 100).toFixed(1) + '%' : '暂无' }}</div>
              <div>平均薪资: {{ row.extra_info.average_salary ? '¥' + row.extra_info.average_salary : '暂无' }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { School, Reading, TrendCharts, DataAnalysis } from '@element-plus/icons-vue'
// import VChart from 'vue-echarts'

// 响应式数据
const loading = ref(false)
const overviewStats = ref([
  {
    title: '总院校数',
    value: '2,856',
    icon: School,
    color: '#409eff'
  },
  {
    title: '总专业数',
    value: '12,345',
    icon: Reading,
    color: '#67c23a'
  },
  {
    title: '录取记录',
    value: '1,234,567',
    icon: DataAnalysis,
    color: '#e6a23c'
  },
  {
    title: '最新年份',
    value: '2024',
    icon: TrendCharts,
    color: '#f56c6c'
  }
])

const popularityRanking = ref([])

// 图表参数
const trendParams = ref({
  science_type: 'physics'
})

const rankingParams = ref({
  year: 2024
})

const popularityParams = ref({
  category: ''
})

// 图表配置
const trendChartOption = ref({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['最低分', '平均分', '最高分']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['2020', '2021', '2022', '2023', '2024']
  },
  yAxis: {
    type: 'value',
    name: '分数'
  },
  series: [
    {
      name: '最低分',
      type: 'line',
      data: [520, 525, 530, 535, 540],
      smooth: true
    },
    {
      name: '平均分',
      type: 'line',
      data: [550, 555, 560, 565, 570],
      smooth: true
    },
    {
      name: '最高分',
      type: 'line',
      data: [680, 685, 690, 695, 700],
      smooth: true
    }
  ]
})

const rankingChartOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: ['清华大学', '北京大学', '复旦大学', '上海交大', '浙江大学']
  },
  series: [
    {
      name: '平均录取分',
      type: 'bar',
      data: [695, 690, 680, 675, 670],
      itemStyle: {
        color: '#409eff'
      }
    }
  ]
})

const distributionChartOption = ref({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '分数分布',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: '700+' },
        { value: 735, name: '650-699' },
        { value: 580, name: '600-649' },
        { value: 484, name: '550-599' },
        { value: 300, name: '500-549' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

const competitionChartOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['北京', '上海', '江苏', '浙江', '广东', '山东', '河南', '四川']
  },
  yAxis: {
    type: 'value',
    name: '竞争指数'
  },
  series: [
    {
      name: '竞争指数',
      type: 'bar',
      data: [95, 92, 88, 85, 82, 78, 75, 72],
      itemStyle: {
        color: new Array(8).fill(0).map((_, i) => {
          const colors = ['#ff6b6b', '#ffa726', '#ffcc02', '#66bb6a', '#42a5f5', '#ab47bc', '#26c6da', '#78909c']
          return colors[i]
        })
      }
    }
  ]
})

// 方法
const loadOverviewData = async () => {
  try {
    // 这里应该调用API获取概览数据
    // const response = await analyticsApi.getOverview()
    // overviewStats.value = response.data
  } catch (error) {
    ElMessage.error('加载概览数据失败')
  }
}

const loadTrendData = async () => {
  try {
    // 这里应该调用API获取趋势数据
    // const response = await analyticsApi.getTrends(trendParams.value)
    // 更新图表数据
  } catch (error) {
    ElMessage.error('加载趋势数据失败')
  }
}

const loadRankingData = async () => {
  try {
    // 这里应该调用API获取排名数据
    // const response = await analyticsApi.getRankings(rankingParams.value)
    // 更新图表数据
  } catch (error) {
    ElMessage.error('加载排名数据失败')
  }
}

const loadPopularityData = async () => {
  try {
    // 这里应该调用API获取热度数据
    // const response = await analyticsApi.getPopularity(popularityParams.value)
    
    // 模拟数据
    popularityRanking.value = [
      {
        rank: 1,
        name: '计算机科学与技术',
        category: '工学',
        value: 95.5,
        count: 50000,
        extra_info: {
          employment_rate: 0.92,
          average_salary: 12000
        }
      },
      {
        rank: 2,
        name: '软件工程',
        category: '工学',
        value: 88.2,
        count: 45000,
        extra_info: {
          employment_rate: 0.89,
          average_salary: 11000
        }
      },
      {
        rank: 3,
        name: '人工智能',
        category: '工学',
        value: 82.1,
        count: 30000,
        extra_info: {
          employment_rate: 0.95,
          average_salary: 15000
        }
      }
    ]
  } catch (error) {
    ElMessage.error('加载热度数据失败')
  }
}

// 生命周期
onMounted(() => {
  loadOverviewData()
  loadTrendData()
  loadRankingData()
  loadPopularityData()
})
</script>

<style lang="scss" scoped>
.analytics-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    p {
      color: #909399;
      margin: 0;
    }
  }
  
  .overview-stats {
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .stat-info {
          flex: 1;
          
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .stat-title {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    
    .header-controls {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }
  
  .chart-container {
    height: 300px;

    .chart-placeholder {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #909399;
      font-size: 14px;
      background: #f5f7fa;
      border-radius: 4px;
    }
  }
  
  .score-text {
    margin-left: 8px;
    font-size: 12px;
    color: #606266;
  }
}
</style>
