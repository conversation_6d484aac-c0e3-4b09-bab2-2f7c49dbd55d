<template>
  <div class="analytics-page">
    <div class="page-header">
      <h1>📊 数据分析</h1>
      <p>高考录取数据统计分析和可视化展示</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card mb-20">
      <template #header>
        <span>筛选条件</span>
      </template>
      <el-form :model="filters" inline>
        <el-form-item label="年份">
          <el-select v-model="filters.year" placeholder="选择年份" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="2024" value="2024" />
            <el-option label="2023" value="2023" />
            <el-option label="2022" value="2022" />
          </el-select>
        </el-form-item>
        <el-form-item label="科类">
          <el-select v-model="filters.science_type" placeholder="选择科类" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="物理类" value="physics" />
            <el-option label="历史类" value="history" />
          </el-select>
        </el-form-item>
        <el-form-item label="省份">
          <el-select v-model="filters.province" placeholder="选择省份" style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="重庆" value="重庆" />
            <el-option label="四川" value="四川" />
            <el-option label="北京" value="北京" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadAnalyticsData" :loading="loading">
            查询分析
          </el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据概览 -->
    <div class="overview-cards mb-20">
      <el-row :gutter="20">
        <el-col :span="6" v-for="stat in overviewStats" :key="stat.title">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                <el-icon :size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-title">{{ stat.title }}</div>
              </div>
            </div>
            <div class="stat-trend" v-if="stat.trend">
              <el-icon :class="stat.trend > 0 ? 'trend-up' : 'trend-down'">
                <ArrowUp v-if="stat.trend > 0" />
                <ArrowDown v-else />
              </el-icon>
              <span>{{ Math.abs(stat.trend) }}%</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb-20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>录取分数趋势</span>
              <el-select v-model="chartType" size="small" style="width: 120px">
                <el-option label="折线图" value="line" />
                <el-option label="柱状图" value="bar" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              📈 分数趋势图表
              <p>显示不同年份的录取分数变化趋势</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>专业分布</span>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              🥧 专业分布饼图
              <p>显示各专业类别的录取人数分布</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>详细数据</span>
          <div class="header-actions">
            <el-button :icon="Download" @click="exportData" size="small">
              导出数据
            </el-button>
            <el-button :icon="Refresh" @click="loadTableData" :loading="tableLoading" size="small">
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="tableData" 
        v-loading="tableLoading"
        stripe
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column prop="school_name" label="学校名称" min-width="200" />
        <el-table-column prop="major_name" label="专业名称" min-width="150" />
        <el-table-column prop="year" label="年份" width="80" />
        <el-table-column prop="science_type" label="科类" width="80">
          <template #default="{ row }">
            <el-tag :type="row.science_type === 'physics' ? 'primary' : 'success'" size="small">
              {{ row.science_type === 'physics' ? '物理' : '历史' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="min_score" label="最低分" width="100" />
        <el-table-column prop="avg_score" label="平均分" width="100" />
        <el-table-column prop="max_score" label="最高分" width="100" />
        <el-table-column prop="enrollment_count" label="录取人数" width="100" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  School,
  Reading,
  TrendCharts,
  DataAnalysis,
  ArrowUp,
  ArrowDown,
  Download,
  Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableLoading = ref(false)
const chartType = ref('line')

const filters = ref({
  year: '',
  science_type: '',
  province: ''
})

const pagination = ref({
  current: 1,
  size: 20,
  total: 0
})

const overviewStats = ref([
  {
    title: '总学校数',
    value: '2,856',
    icon: School,
    color: '#409eff',
    trend: 5.2
  },
  {
    title: '总专业数',
    value: '15,432',
    icon: Reading,
    color: '#67c23a',
    trend: 8.1
  },
  {
    title: '录取数据',
    value: '1,234,567',
    icon: TrendCharts,
    color: '#e6a23c',
    trend: -2.3
  },
  {
    title: '分析报告',
    value: '89',
    icon: DataAnalysis,
    color: '#f56c6c',
    trend: 12.5
  }
])

const tableData = ref([
  {
    school_name: '清华大学',
    major_name: '计算机科学与技术',
    year: '2024',
    science_type: 'physics',
    min_score: 685,
    avg_score: 692,
    max_score: 698,
    enrollment_count: 45
  },
  {
    school_name: '北京大学',
    major_name: '软件工程',
    year: '2024',
    science_type: 'physics',
    min_score: 680,
    avg_score: 687,
    max_score: 695,
    enrollment_count: 38
  },
  {
    school_name: '复旦大学',
    major_name: '金融学',
    year: '2024',
    science_type: 'history',
    min_score: 645,
    avg_score: 652,
    max_score: 660,
    enrollment_count: 52
  }
])

// 方法
const loadAnalyticsData = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取分析数据
    // const response = await analyticsApi.getOverviewData(filters.value)
    // overviewStats.value = response.data
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据加载完成')
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadTableData = async () => {
  tableLoading.value = true
  try {
    // 这里应该调用API获取表格数据
    // const response = await analyticsApi.getDetailData({
    //   ...filters.value,
    //   page: pagination.value.current,
    //   size: pagination.value.size
    // })
    // tableData.value = response.data
    // pagination.value.total = response.total
    
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    ElMessage.error('加载表格数据失败')
  } finally {
    tableLoading.value = false
  }
}

const resetFilters = () => {
  filters.value = {
    year: '',
    science_type: '',
    province: ''
  }
  loadAnalyticsData()
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSizeChange = (size: number) => {
  pagination.value.size = size
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.value.current = page
  loadTableData()
}

// 生命周期
onMounted(() => {
  loadAnalyticsData()
  loadTableData()
})
</script>

<style lang="scss" scoped>
.analytics-page {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 28px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .filter-card {
    .el-form {
      margin-bottom: 0;
    }
  }

  .overview-cards {
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .stat-title {
            font-size: 14px;
            color: #909399;
          }
        }
      }

      .stat-trend {
        margin-top: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;

        &.trend-up {
          color: #67c23a;
        }

        &.trend-down {
          color: #f56c6c;
        }
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;

    .chart-placeholder {
      text-align: center;
      color: #909399;
      font-size: 48px;

      p {
        font-size: 14px;
        margin-top: 16px;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
