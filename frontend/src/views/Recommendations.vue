<template>
  <div class="recommendations-page">
    <div class="page-header">
      <h1>专业推荐</h1>
      <p>基于位次的智能专业推荐系统</p>
    </div>

    <!-- 推荐配置 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>推荐配置</span>
          <el-button type="primary" size="small" @click="showConfigDialog = true">
            新建配置
          </el-button>
        </div>
      </template>
      
      <el-form :model="recommendForm" label-width="120px" class="recommend-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="用户位次">
              <el-input-number 
                v-model="recommendForm.user_rank" 
                :min="1" 
                :max="200000"
                placeholder="请输入位次"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="科类">
              <el-select v-model="recommendForm.science_type" style="width: 100%">
                <el-option label="物理类" value="physics" />
                <el-option label="历史类" value="history" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="目标年份">
              <el-select v-model="recommendForm.target_year" style="width: 100%">
                <el-option label="2024年" :value="2024" />
                <el-option label="2023年" :value="2023" />
                <el-option label="2022年" :value="2022" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="generateRecommendations"
            :loading="generating"
            :disabled="!recommendForm.user_rank"
          >
            生成推荐
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 推荐结果 -->
    <el-card v-if="recommendations.length > 0">
      <template #header>
        <div class="card-header">
          <span>推荐结果</span>
          <div class="result-stats">
            <el-tag type="danger">冲刺 {{ rushCount }}</el-tag>
            <el-tag type="warning">稳妥 {{ stableCount }}</el-tag>
            <el-tag type="success">保底 {{ safeCount }}</el-tag>
          </div>
        </div>
      </template>

      <!-- 筛选器 -->
      <div class="filters mb-20">
        <el-select v-model="typeFilter" placeholder="推荐类型" clearable style="width: 120px">
          <el-option label="冲刺" value="rush" />
          <el-option label="稳妥" value="stable" />
          <el-option label="保底" value="safe" />
        </el-select>
        <el-input 
          v-model="searchKeyword" 
          placeholder="搜索学校或专业"
          style="width: 200px; margin-left: 12px"
          clearable
        />
      </div>

      <!-- 推荐列表 -->
      <el-table 
        :data="filteredRecommendations" 
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="overall_rank" label="排名" width="80" />
        <el-table-column label="推荐类型" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getTypeColor(row.recommendation_type)"
              size="small"
            >
              {{ getTypeText(row.recommendation_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="university_name" label="学校" width="200" />
        <el-table-column prop="major_name" label="专业" width="200" />
        <el-table-column label="学校层次" width="120">
          <template #default="{ row }">
            <div class="school-tags">
              <el-tag v-if="row.is_985" type="danger" size="small">985</el-tag>
              <el-tag v-if="row.is_211" type="warning" size="small">211</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="province" label="省份" width="100" />
        <el-table-column label="录取分数" width="120">
          <template #default="{ row }">
            {{ row.min_score }}-{{ row.max_score }}
          </template>
        </el-table-column>
        <el-table-column label="录取位次" width="120">
          <template #default="{ row }">
            {{ row.max_rank }}-{{ row.min_rank }}
          </template>
        </el-table-column>
        <el-table-column 
          prop="total_score" 
          label="推荐分数" 
          width="100"
          sortable="custom"
        >
          <template #default="{ row }">
            <el-progress 
              :percentage="row.total_score" 
              :stroke-width="6"
              :show-text="false"
            />
            <span class="score-text">{{ row.total_score.toFixed(1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button 
              size="small" 
              :type="row.is_favorited ? 'warning' : 'primary'"
              :icon="row.is_favorited ? StarFilled : Star"
              @click="toggleFavorite(row)"
            >
              {{ row.is_favorited ? '取消收藏' : '收藏' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="filteredRecommendations.length"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>

    <!-- 推荐历史 -->
    <el-card class="mt-20">
      <template #header>
        <div class="card-header">
          <span>推荐历史</span>
        </div>
      </template>
      
      <el-table :data="history" stripe>
        <el-table-column prop="user_rank" label="位次" width="100" />
        <el-table-column prop="science_type" label="科类" width="100" />
        <el-table-column prop="target_year" label="年份" width="100" />
        <el-table-column prop="total_recommendations" label="推荐数量" width="120" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="viewHistory(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Star, StarFilled } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const generating = ref(false)
const showConfigDialog = ref(false)
const recommendations = ref([])
const history = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const typeFilter = ref('')
const searchKeyword = ref('')

// 表单数据
const recommendForm = ref({
  user_rank: null,
  science_type: 'physics',
  target_year: 2024,
  config_id: null
})

// 计算属性
const rushCount = computed(() => 
  recommendations.value.filter((item: any) => item.recommendation_type === 'rush').length
)

const stableCount = computed(() => 
  recommendations.value.filter((item: any) => item.recommendation_type === 'stable').length
)

const safeCount = computed(() => 
  recommendations.value.filter((item: any) => item.recommendation_type === 'safe').length
)

const filteredRecommendations = computed(() => {
  let filtered = recommendations.value

  // 类型筛选
  if (typeFilter.value) {
    filtered = filtered.filter((item: any) => item.recommendation_type === typeFilter.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter((item: any) => 
      item.university_name.toLowerCase().includes(keyword) ||
      item.major_name.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 方法
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    rush: 'danger',
    stable: 'warning',
    safe: 'success'
  }
  return colorMap[type] || 'info'
}

const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    rush: '冲刺',
    stable: '稳妥',
    safe: '保底'
  }
  return textMap[type] || type
}

const generateRecommendations = async () => {
  if (!recommendForm.value.user_rank) {
    ElMessage.warning('请输入用户位次')
    return
  }

  generating.value = true
  try {
    // 这里应该调用API生成推荐
    // const response = await recommendationApi.generate(recommendForm.value)
    
    // 模拟数据
    recommendations.value = [
      {
        id: 1,
        overall_rank: 1,
        recommendation_type: 'rush',
        university_name: '清华大学',
        major_name: '计算机科学与技术',
        is_985: true,
        is_211: true,
        province: '北京',
        min_score: 680,
        max_score: 720,
        min_rank: 100,
        max_rank: 50,
        total_score: 95.5,
        is_favorited: false
      },
      {
        id: 2,
        overall_rank: 2,
        recommendation_type: 'stable',
        university_name: '华中科技大学',
        major_name: '软件工程',
        is_985: true,
        is_211: true,
        province: '湖北',
        min_score: 650,
        max_score: 680,
        min_rank: 500,
        max_rank: 200,
        total_score: 88.2,
        is_favorited: false
      }
    ]
    
    ElMessage.success('推荐生成成功')
    await loadHistory()
  } catch (error) {
    ElMessage.error('推荐生成失败')
  } finally {
    generating.value = false
  }
}

const resetForm = () => {
  recommendForm.value = {
    user_rank: null,
    science_type: 'physics',
    target_year: 2024,
    config_id: null
  }
  recommendations.value = []
}

const toggleFavorite = async (row: any) => {
  try {
    // 这里应该调用API切换收藏状态
    row.is_favorited = !row.is_favorited
    ElMessage.success(row.is_favorited ? '收藏成功' : '取消收藏成功')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleSortChange = ({ prop, order }: any) => {
  // 处理排序
  if (prop === 'total_score') {
    recommendations.value.sort((a: any, b: any) => {
      return order === 'ascending' ? a.total_score - b.total_score : b.total_score - a.total_score
    })
  }
}

const loadHistory = async () => {
  try {
    // 这里应该调用API获取历史记录
    // const response = await recommendationApi.getHistory()
    // history.value = response.data
    
    // 模拟数据
    history.value = [
      {
        id: 1,
        user_rank: 15000,
        science_type: '物理类',
        target_year: 2024,
        total_recommendations: 100,
        created_at: '2024-03-20T10:30:00Z'
      }
    ]
  } catch (error) {
    ElMessage.error('加载历史记录失败')
  }
}

const viewHistory = (row: any) => {
  // 查看历史详情
  ElMessage.info('查看历史详情功能开发中...')
}

// 生命周期
onMounted(() => {
  loadHistory()
})
</script>

<style lang="scss" scoped>
.recommendations-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    p {
      color: #909399;
      margin: 0;
    }
  }
  
  .recommend-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
  
  .result-stats {
    display: flex;
    gap: 8px;
  }
  
  .filters {
    display: flex;
    align-items: center;
  }
  
  .school-tags {
    display: flex;
    gap: 4px;
  }
  
  .score-text {
    margin-left: 8px;
    font-size: 12px;
    color: #606266;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
