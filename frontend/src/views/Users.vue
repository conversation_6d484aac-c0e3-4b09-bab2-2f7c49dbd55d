<template>
  <div class="users-page">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理系统用户和权限设置</p>
    </div>

    <!-- 操作栏 -->
    <el-card class="mb-20">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
          <el-button @click="refreshUsers">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-input 
            v-model="searchKeyword" 
            placeholder="搜索用户名或邮箱"
            style="width: 200px"
            clearable
          />
          <el-select v-model="roleFilter" placeholder="角色筛选" clearable style="width: 120px">
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
          <el-select v-model="statusFilter" placeholder="状态筛选" clearable style="width: 120px">
            <el-option label="激活" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <el-card>
      <el-table 
        :data="filteredUsers" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="full_name" label="真实姓名" width="120" />
        <el-table-column label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录" width="180">
          <template #default="{ row }">
            {{ row.last_login ? formatTime(row.last_login) : '从未登录' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="editUser(row)">
                编辑
              </el-button>
              <el-button 
                size="small" 
                :type="row.is_active ? 'warning' : 'success'"
                @click="toggleUserStatus(row)"
              >
                {{ row.is_active ? '禁用' : '激活' }}
              </el-button>
              <el-button 
                size="small" 
                type="info"
                @click="resetPassword(row)"
              >
                重置密码
              </el-button>
              <el-button 
                size="small" 
                type="danger"
                @click="deleteUser(row)"
                :disabled="row.id === currentUserId"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingUser ? '编辑用户' : '新增用户'"
      width="600px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="userForm.username" 
            placeholder="请输入用户名"
            :disabled="!!editingUser"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input v-model="userForm.full_name" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" style="width: 100%">
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!editingUser" label="密码" prop="password">
          <el-input 
            v-model="userForm.password" 
            type="password" 
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch 
            v-model="userForm.is_active" 
            active-text="激活" 
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button 
          type="primary" 
          @click="saveUser"
          :loading="saveLoading"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const showCreateDialog = ref(false)
const editingUser = ref(null)
const users = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const searchKeyword = ref('')
const roleFilter = ref('')
const statusFilter = ref('')

const userFormRef = ref<FormInstance>()

// 表单数据
const userForm = ref({
  username: '',
  email: '',
  full_name: '',
  role: 'user',
  password: '',
  is_active: true
})

// 表单验证规则
const userRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const currentUserId = computed(() => authStore.user?.id)

const filteredUsers = computed(() => {
  let filtered = users.value

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter((user: any) => 
      user.username.toLowerCase().includes(keyword) ||
      user.email.toLowerCase().includes(keyword)
    )
  }

  // 角色筛选
  if (roleFilter.value) {
    filtered = filtered.filter((user: any) => user.role === roleFilter.value)
  }

  // 状态筛选
  if (statusFilter.value !== '') {
    filtered = filtered.filter((user: any) => user.is_active === statusFilter.value)
  }

  return filtered
})

// 方法
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const getRoleType = (role: string) => {
  const typeMap: Record<string, string> = {
    super_admin: 'danger',
    admin: 'warning',
    user: 'info'
  }
  return typeMap[role] || 'info'
}

const getRoleText = (role: string) => {
  const textMap: Record<string, string> = {
    super_admin: '超级管理员',
    admin: '管理员',
    user: '普通用户'
  }
  return textMap[role] || role
}

const loadUsers = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取用户列表
    // const response = await userApi.getUsers({
    //   page: currentPage.value,
    //   size: pageSize.value
    // })
    
    // 模拟数据
    users.value = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        full_name: '系统管理员',
        role: 'super_admin',
        is_active: true,
        last_login: '2024-03-20T10:30:00Z',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        username: 'user1',
        email: '<EMAIL>',
        full_name: '张三',
        role: 'user',
        is_active: true,
        last_login: '2024-03-19T15:20:00Z',
        created_at: '2024-02-01T00:00:00Z'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const refreshUsers = () => {
  loadUsers()
}

const editUser = (user: any) => {
  editingUser.value = user
  userForm.value = {
    username: user.username,
    email: user.email,
    full_name: user.full_name || '',
    role: user.role,
    password: '',
    is_active: user.is_active
  }
  showCreateDialog.value = true
}

const saveUser = async () => {
  if (!userFormRef.value) return
  
  const valid = await userFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  saveLoading.value = true
  try {
    if (editingUser.value) {
      // 更新用户
      // await userApi.updateUser(editingUser.value.id, userForm.value)
      ElMessage.success('用户更新成功')
    } else {
      // 创建用户
      // await userApi.createUser(userForm.value)
      ElMessage.success('用户创建成功')
    }
    
    showCreateDialog.value = false
    await loadUsers()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const cancelEdit = () => {
  showCreateDialog.value = false
  editingUser.value = null
  userForm.value = {
    username: '',
    email: '',
    full_name: '',
    role: 'user',
    password: '',
    is_active: true
  }
}

const toggleUserStatus = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要${user.is_active ? '禁用' : '激活'}用户 ${user.username} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API切换用户状态
    // await userApi.toggleUserStatus(user.id)
    
    user.is_active = !user.is_active
    ElMessage.success('操作成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const resetPassword = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 ${user.username} 的密码吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API重置密码
    // await userApi.resetPassword(user.id, 'newPassword123')
    
    ElMessage.success('密码重置成功，新密码为：123456')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败')
    }
  }
}

const deleteUser = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${user.username} 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    // 这里应该调用API删除用户
    // await userApi.deleteUser(user.id)
    
    ElMessage.success('用户删除成功')
    await loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadUsers()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadUsers()
}

// 生命周期
onMounted(() => {
  loadUsers()
})
</script>

<style lang="scss" scoped>
.users-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    p {
      color: #909399;
      margin: 0;
    }
  }
  
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .toolbar-left {
      display: flex;
      gap: 12px;
    }
    
    .toolbar-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
