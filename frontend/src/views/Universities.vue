<template>
  <div class="universities-page">
    <div class="page-header">
      <h1>大学管理</h1>
      <p>查看和管理高校信息数据</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="mb-20">
      <el-form :model="searchForm" inline>
        <el-form-item label="学校名称">
          <el-input 
            v-model="searchForm.search" 
            placeholder="请输入学校名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="省份">
          <el-select v-model="searchForm.province" placeholder="选择省份" clearable>
            <el-option label="北京" value="北京" />
            <el-option label="上海" value="上海" />
            <el-option label="广东" value="广东" />
            <el-option label="江苏" value="江苏" />
            <el-option label="浙江" value="浙江" />
          </el-select>
        </el-form-item>
        <el-form-item label="学校类型">
          <el-select v-model="searchForm.schoolType" placeholder="选择类型" clearable>
            <el-option label="985高校" value="985" />
            <el-option label="211高校" value="211" />
            <el-option label="双一流" value="double_first_class" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchUniversities">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 大学列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>大学列表</span>
          <div class="header-actions">
            <el-button :icon="Download" @click="exportData">导出数据</el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="universities" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="code" label="学校代码" width="100" />
        <el-table-column prop="name" label="学校名称" width="200" />
        <el-table-column prop="province" label="省份" width="100" />
        <el-table-column prop="city" label="城市" width="100" />
        <el-table-column label="学校层次" width="150">
          <template #default="{ row }">
            <div class="school-tags">
              <el-tag v-if="row.is_985" type="danger" size="small">985</el-tag>
              <el-tag v-if="row.is_211" type="warning" size="small">211</el-tag>
              <el-tag v-if="row.is_double_first_class" type="success" size="small">双一流</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="school_type" label="学校类型" width="120" />
        <el-table-column prop="total_majors" label="专业数量" width="100" />
        <el-table-column prop="ranking_national" label="全国排名" width="100" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="学校详情"
      width="800px"
    >
      <div v-if="currentUniversity" class="university-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="学校代码">
            {{ currentUniversity.code }}
          </el-descriptions-item>
          <el-descriptions-item label="学校名称">
            {{ currentUniversity.name }}
          </el-descriptions-item>
          <el-descriptions-item label="英文名称">
            {{ currentUniversity.english_name || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="所在省份">
            {{ currentUniversity.province }}
          </el-descriptions-item>
          <el-descriptions-item label="所在城市">
            {{ currentUniversity.city || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="学校性质">
            {{ currentUniversity.school_nature || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="学校类型">
            {{ currentUniversity.school_type || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="专业数量">
            {{ currentUniversity.total_majors }}
          </el-descriptions-item>
          <el-descriptions-item label="全国排名">
            {{ currentUniversity.ranking_national || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="省内排名">
            {{ currentUniversity.ranking_provincial || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="官方网站" span="2">
            <el-link 
              v-if="currentUniversity.website" 
              :href="currentUniversity.website" 
              target="_blank"
            >
              {{ currentUniversity.website }}
            </el-link>
            <span v-else>暂无</span>
          </el-descriptions-item>
          <el-descriptions-item label="详细地址" span="2">
            {{ currentUniversity.address || '暂无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const universities = ref([])
const currentUniversity = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 搜索表单
const searchForm = ref({
  search: '',
  province: '',
  schoolType: ''
})

// 方法
const searchUniversities = async () => {
  loading.value = true
  try {
    // 这里应该调用API搜索大学
    // const response = await universityApi.getUniversities({
    //   ...searchForm.value,
    //   page: currentPage.value,
    //   size: pageSize.value
    // })
    
    // 模拟数据
    universities.value = [
      {
        id: 1,
        code: '10001',
        name: '北京大学',
        english_name: 'Peking University',
        province: '北京',
        city: '北京',
        is_985: true,
        is_211: true,
        is_double_first_class: true,
        school_type: '综合类',
        school_nature: '公办',
        total_majors: 125,
        ranking_national: 1,
        ranking_provincial: 1,
        website: 'https://www.pku.edu.cn',
        address: '北京市海淀区颐和园路5号'
      },
      {
        id: 2,
        code: '10002',
        name: '清华大学',
        english_name: 'Tsinghua University',
        province: '北京',
        city: '北京',
        is_985: true,
        is_211: true,
        is_double_first_class: true,
        school_type: '理工类',
        school_nature: '公办',
        total_majors: 118,
        ranking_national: 2,
        ranking_provincial: 2,
        website: 'https://www.tsinghua.edu.cn',
        address: '北京市海淀区清华园1号'
      }
    ]
    total.value = 2856
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.value = {
    search: '',
    province: '',
    schoolType: ''
  }
  searchUniversities()
}

const viewDetail = (university: any) => {
  currentUniversity.value = university
  showDetailDialog.value = true
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  searchUniversities()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  searchUniversities()
}

// 生命周期
onMounted(() => {
  searchUniversities()
})
</script>

<style lang="scss" scoped>
.universities-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    p {
      color: #909399;
      margin: 0;
    }
  }
  
  .school-tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
  
  .university-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
