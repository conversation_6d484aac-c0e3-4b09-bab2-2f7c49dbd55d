<template>
  <div class="majors-page">
    <div class="page-header">
      <h1>专业管理</h1>
      <p>查看和管理专业信息数据</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="mb-20">
      <el-form :model="searchForm" inline>
        <el-form-item label="专业名称">
          <el-input 
            v-model="searchForm.search" 
            placeholder="请输入专业名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="专业大类">
          <el-select v-model="searchForm.category" placeholder="选择专业大类" clearable>
            <el-option label="工学" value="工学" />
            <el-option label="理学" value="理学" />
            <el-option label="经济学" value="经济学" />
            <el-option label="管理学" value="管理学" />
            <el-option label="文学" value="文学" />
            <el-option label="医学" value="医学" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序方式">
          <el-select v-model="searchForm.orderBy" placeholder="选择排序">
            <el-option label="专业名称" value="name" />
            <el-option label="热度分数" value="popularity" />
            <el-option label="就业率" value="employment_rate" />
            <el-option label="平均薪资" value="salary" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchMajors">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 专业列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>专业列表</span>
          <div class="header-actions">
            <el-button :icon="TrendCharts" @click="showRanking = true">热度排行</el-button>
            <el-button :icon="Download" @click="exportData">导出数据</el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="majors" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="code" label="专业代码" width="100" />
        <el-table-column prop="name" label="专业名称" width="200" />
        <el-table-column prop="category" label="专业大类" width="120" />
        <el-table-column prop="degree_type" label="学位类型" width="100" />
        <el-table-column prop="duration" label="学制" width="80">
          <template #default="{ row }">
            {{ row.duration }}年
          </template>
        </el-table-column>
        <el-table-column label="热度分数" width="120">
          <template #default="{ row }">
            <el-progress 
              :percentage="Math.min(row.popularity_score, 100)" 
              :stroke-width="6"
              :show-text="false"
            />
            <span class="score-text">{{ row.popularity_score.toFixed(1) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="employment_rate" label="就业率" width="100">
          <template #default="{ row }">
            {{ row.employment_rate ? (row.employment_rate * 100).toFixed(1) + '%' : '暂无' }}
          </template>
        </el-table-column>
        <el-table-column prop="average_salary" label="平均薪资" width="100">
          <template #default="{ row }">
            {{ row.average_salary ? '¥' + row.average_salary : '暂无' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_universities" label="开设院校" width="100" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="专业详情"
      width="800px"
    >
      <div v-if="currentMajor" class="major-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="专业代码">
            {{ currentMajor.code || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="专业名称">
            {{ currentMajor.name }}
          </el-descriptions-item>
          <el-descriptions-item label="专业大类">
            {{ currentMajor.category || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="学位类型">
            {{ currentMajor.degree_type || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="学制年限">
            {{ currentMajor.duration }}年
          </el-descriptions-item>
          <el-descriptions-item label="热度分数">
            {{ currentMajor.popularity_score.toFixed(1) }}
          </el-descriptions-item>
          <el-descriptions-item label="就业率">
            {{ currentMajor.employment_rate ? (currentMajor.employment_rate * 100).toFixed(1) + '%' : '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="平均薪资">
            {{ currentMajor.average_salary ? '¥' + currentMajor.average_salary : '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="总录取人数">
            {{ currentMajor.total_enrollments }}
          </el-descriptions-item>
          <el-descriptions-item label="开设院校数">
            {{ currentMajor.total_universities }}
          </el-descriptions-item>
          <el-descriptions-item label="专业描述" span="2">
            {{ currentMajor.description || '暂无' }}
          </el-descriptions-item>
          <el-descriptions-item label="就业前景" span="2">
            {{ currentMajor.employment_prospects || '暂无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 热度排行对话框 -->
    <el-dialog
      v-model="showRanking"
      title="专业热度排行榜"
      width="600px"
    >
      <el-table :data="rankingData" stripe>
        <el-table-column prop="rank" label="排名" width="80" />
        <el-table-column prop="name" label="专业名称" />
        <el-table-column prop="category" label="专业大类" width="120" />
        <el-table-column label="热度分数" width="120">
          <template #default="{ row }">
            <el-progress 
              :percentage="Math.min(row.popularity_score, 100)" 
              :stroke-width="6"
              :show-text="false"
            />
            <span class="score-text">{{ row.popularity_score.toFixed(1) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts, Download } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const showRanking = ref(false)
const majors = ref([])
const rankingData = ref([])
const currentMajor = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 搜索表单
const searchForm = ref({
  search: '',
  category: '',
  orderBy: 'name'
})

// 方法
const searchMajors = async () => {
  loading.value = true
  try {
    // 这里应该调用API搜索专业
    // const response = await majorApi.getMajors({
    //   ...searchForm.value,
    //   page: currentPage.value,
    //   size: pageSize.value
    // })
    
    // 模拟数据
    majors.value = [
      {
        id: 1,
        code: '080901',
        name: '计算机科学与技术',
        category: '工学',
        degree_type: '工学学士',
        duration: 4,
        popularity_score: 95.5,
        employment_rate: 0.92,
        average_salary: 12000,
        total_enrollments: 50000,
        total_universities: 500,
        description: '计算机科学与技术专业培养具有良好的科学素养...',
        employment_prospects: '毕业生可在IT企业、科研院所等从事...'
      },
      {
        id: 2,
        code: '080902',
        name: '软件工程',
        category: '工学',
        degree_type: '工学学士',
        duration: 4,
        popularity_score: 88.2,
        employment_rate: 0.89,
        average_salary: 11000,
        total_enrollments: 45000,
        total_universities: 450,
        description: '软件工程专业培养能够从事软件开发...',
        employment_prospects: '毕业生主要在软件公司、互联网企业...'
      }
    ]
    total.value = 12345
    
    // 加载排行数据
    rankingData.value = majors.value.map((major, index) => ({
      ...major,
      rank: index + 1
    })).sort((a, b) => b.popularity_score - a.popularity_score).slice(0, 10)
    
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.value = {
    search: '',
    category: '',
    orderBy: 'name'
  }
  searchMajors()
}

const viewDetail = (major: any) => {
  currentMajor.value = major
  showDetailDialog.value = true
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  searchMajors()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  searchMajors()
}

// 生命周期
onMounted(() => {
  searchMajors()
})
</script>

<style lang="scss" scoped>
.majors-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    p {
      color: #909399;
      margin: 0;
    }
  }
  
  .score-text {
    margin-left: 8px;
    font-size: 12px;
    color: #606266;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
  
  .major-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
