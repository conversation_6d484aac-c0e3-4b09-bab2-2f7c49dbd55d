<template>
  <div class="crawler-page">
    <div class="page-header">
      <h1>🕷️ 爬虫管理</h1>
      <p>管理高考数据爬取任务，监控执行状态和查看日志</p>
    </div>

    <!-- 系统状态 -->
    <SystemStatus class="mb-20" />

    <!-- 任务管理 -->
    <CrawlerTaskManager class="mb-20" />

    <!-- 实时日志 -->
    <RealTimeLog />

  </div>
</template>

<script setup lang="ts">
import SystemStatus from '@/components/SystemStatus.vue'
import CrawlerTaskManager from '@/components/CrawlerTaskManager.vue'
import RealTimeLog from '@/components/RealTimeLog.vue'
</script>

<style lang="scss" scoped>
.crawler-page {
  .page-header {
    margin-bottom: 24px;

    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 28px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }
}

      <el-table 
        :data="tasks" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="任务ID" width="80" />
        <el-table-column prop="data_type" label="任务类型" width="150" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进度" width="200">
          <template #default="{ row }">
            <div class="progress-container">
              <el-progress 
                :percentage="row.progress_percentage" 
                :status="getProgressStatus(row.status)"
                :stroke-width="8"
              />
              <span class="progress-text">
                {{ row.processed_records }}/{{ row.total_records }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button-group>
              <el-button 
                size="small" 
                :icon="View"
                @click="viewTaskLogs(row)"
              >
                日志
              </el-button>
              <el-button 
                v-if="row.status === 'running'"
                size="small" 
                type="warning"
                :icon="VideoPause"
                @click="controlTask(row.id, 'pause')"
              >
                暂停
              </el-button>
              <el-button 
                v-if="row.status === 'paused'"
                size="small" 
                type="success"
                :icon="VideoPlay"
                @click="controlTask(row.id, 'resume')"
              >
                恢复
              </el-button>
              <el-button 
                v-if="['running', 'paused'].includes(row.status)"
                size="small" 
                type="danger"
                :icon="Close"
                @click="controlTask(row.id, 'cancel')"
              >
                取消
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建任务对话框 -->
    <el-dialog
      v-model="showCreateTaskDialog"
      title="创建爬取任务"
      width="600px"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskRules"
        label-width="120px"
      >
        <el-form-item label="任务名称" prop="task_name">
          <el-input v-model="taskForm.task_name" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="爬取年份" prop="years">
          <el-select 
            v-model="taskForm.years" 
            multiple 
            placeholder="选择要爬取的年份"
            style="width: 100%"
          >
            <el-option label="2024年" value="2024" />
            <el-option label="2023年" value="2023" />
            <el-option label="2022年" value="2022" />
            <el-option label="2021年" value="2021" />
          </el-select>
        </el-form-item>
        <el-form-item label="科类" prop="science_types">
          <el-select 
            v-model="taskForm.science_types" 
            multiple 
            placeholder="选择科类"
            style="width: 100%"
          >
            <el-option label="物理类" value="physics" />
            <el-option label="历史类" value="history" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input 
            v-model="taskForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入任务描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateTaskDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="createTask"
          :loading="createLoading"
        >
          创建任务
        </el-button>
      </template>
    </el-dialog>

    <!-- 日志查看对话框 -->
    <el-dialog
      v-model="showLogsDialog"
      title="任务日志"
      width="80%"
      top="5vh"
    >
      <div class="logs-container">
        <div class="logs-header">
          <el-select v-model="logLevel" placeholder="日志级别" clearable style="width: 120px">
            <el-option label="DEBUG" value="DEBUG" />
            <el-option label="INFO" value="INFO" />
            <el-option label="WARNING" value="WARNING" />
            <el-option label="ERROR" value="ERROR" />
          </el-select>
          <el-button :icon="Refresh" @click="refreshLogs">刷新</el-button>
        </div>
        <div class="logs-content" ref="logsContainer">
          <div 
            v-for="log in logs" 
            :key="log.id"
            :class="['log-item', `log-${log.level.toLowerCase()}`]"
          >
            <span class="log-time">{{ formatTime(log.created_at) }}</span>
            <span class="log-level">{{ log.level }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Link,
  Refresh,
  View,
  VideoPause,
  VideoPlay,
  Close
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const showCreateTaskDialog = ref(false)
const showLogsDialog = ref(false)
const showSystemStatus = ref(false)

const tasks = ref([])
const logs = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const statusFilter = ref('')
const logLevel = ref('')
const currentTaskId = ref(null)

const taskFormRef = ref<FormInstance>()
const logsContainer = ref<HTMLElement>()

// 表单数据
const taskForm = ref({
  task_name: '',
  years: ['2024'],
  science_types: ['physics', 'history'],
  description: ''
})

// 表单验证规则
const taskRules: FormRules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  years: [
    { required: true, message: '请选择要爬取的年份', trigger: 'change' }
  ],
  science_types: [
    { required: true, message: '请选择科类', trigger: 'change' }
  ]
}

// 计算属性
const hasRunningTask = computed(() => {
  return tasks.value.some((task: any) => ['running', 'pending'].includes(task.status))
})

// 方法
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'warning',
    pending: 'info',
    paused: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    pending: '待执行',
    paused: '已暂停'
  }
  return statusMap[status] || status
}

const getProgressStatus = (status: string) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const loadTasks = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取任务列表
    // const response = await crawlerApi.getTasks({
    //   limit: pageSize.value,
    //   offset: (currentPage.value - 1) * pageSize.value,
    //   status_filter: statusFilter.value
    // })
    // tasks.value = response.data
    // total.value = response.total
    
    // 模拟数据
    tasks.value = [
      {
        id: 1,
        data_type: '高考录取数据',
        status: 'completed',
        total_records: 15000,
        processed_records: 15000,
        progress_percentage: 100,
        created_at: '2024-03-20T10:30:00Z'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const createTask = async () => {
  if (!taskFormRef.value) return
  
  const valid = await taskFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  createLoading.value = true
  try {
    // 这里应该调用API创建任务
    // await crawlerApi.createTask(taskForm.value)
    
    ElMessage.success('任务创建成功')
    showCreateTaskDialog.value = false
    
    // 重置表单
    taskForm.value = {
      task_name: '',
      years: ['2024'],
      science_types: ['physics', 'history'],
      description: ''
    }
    
    await loadTasks()
  } catch (error) {
    ElMessage.error('创建任务失败')
  } finally {
    createLoading.value = false
  }
}

const controlTask = async (taskId: number, action: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要${action === 'pause' ? '暂停' : action === 'resume' ? '恢复' : '取消'}此任务吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API控制任务
    // await crawlerApi.controlTask(taskId, { action })
    
    ElMessage.success('操作成功')
    await loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const viewTaskLogs = async (task: any) => {
  currentTaskId.value = task.id
  showLogsDialog.value = true
  await loadLogs()
}

const loadLogs = async () => {
  if (!currentTaskId.value) return
  
  try {
    // 这里应该调用API获取日志
    // const response = await crawlerApi.getTaskLogs(currentTaskId.value, {
    //   level: logLevel.value,
    //   limit: 100
    // })
    // logs.value = response.data
    
    // 模拟数据
    logs.value = [
      {
        id: 1,
        level: 'INFO',
        message: '开始爬取高考数据',
        created_at: '2024-03-20T10:30:00Z'
      },
      {
        id: 2,
        level: 'INFO',
        message: '获取学校列表成功，共计2856所学校',
        created_at: '2024-03-20T10:31:00Z'
      }
    ]
  } catch (error) {
    ElMessage.error('加载日志失败')
  }
}

const refreshTasks = () => {
  loadTasks()
}

const refreshLogs = () => {
  loadLogs()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadTasks()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadTasks()
}

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style lang="scss" scoped>
.crawler-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    p {
      color: #909399;
      margin: 0;
    }
  }
  
  .quick-actions {
    display: flex;
    gap: 12px;
  }
  
  .progress-container {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .progress-text {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
  
  .logs-container {
    .logs-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #ebeef5;
    }
    
    .logs-content {
      height: 400px;
      overflow-y: auto;
      background: #f8f9fa;
      border-radius: 4px;
      padding: 12px;
      
      .log-item {
        display: flex;
        gap: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        
        &:last-child {
          border-bottom: none;
        }
        
        .log-time {
          color: #909399;
          white-space: nowrap;
        }
        
        .log-level {
          font-weight: 600;
          width: 60px;
          text-align: center;
          border-radius: 4px;
          padding: 2px 4px;
          
          &.log-debug { background: #f4f4f5; color: #909399; }
          &.log-info { background: #e1f3d8; color: #67c23a; }
          &.log-warning { background: #fdf6ec; color: #e6a23c; }
          &.log-error { background: #fef0f0; color: #f56c6c; }
        }
        
        .log-message {
          flex: 1;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
