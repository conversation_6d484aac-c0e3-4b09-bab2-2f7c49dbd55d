<template>
  <div class="test-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>🧪 系统测试页面</span>
        </div>
      </template>
      
      <div class="test-section">
        <h3>📡 API连接测试</h3>
        <el-space>
          <el-button @click="testHealthAPI" :loading="loading.health">
            测试健康检查
          </el-button>
          <el-button @click="testDirectAPI" :loading="loading.direct">
            测试直连API
          </el-button>
        </el-space>
        
        <div v-if="results.length > 0" class="results">
          <h4>测试结果:</h4>
          <div v-for="(result, index) in results" :key="index" 
               :class="['result-item', result.success ? 'success' : 'error']">
            <el-icon><component :is="result.success ? 'Check' : 'Close'" /></el-icon>
            <span>{{ result.message }}</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>🎨 组件测试</h3>
        <el-space>
          <el-button type="primary" @click="showMessage">显示消息</el-button>
          <el-button type="success" @click="showNotification">显示通知</el-button>
          <el-button type="warning" @click="testIcons">测试图标</el-button>
        </el-space>
      </div>

      <div class="test-section">
        <h3>📊 图标测试</h3>
        <div class="icon-grid">
          <div v-for="icon in testIcons" :key="icon.name" class="icon-item">
            <el-icon :size="24"><component :is="icon.component" /></el-icon>
            <span>{{ icon.name }}</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>🌐 网络信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="前端地址">{{ frontendUrl }}</el-descriptions-item>
          <el-descriptions-item label="后端地址">{{ backendUrl }}</el-descriptions-item>
          <el-descriptions-item label="当前时间">{{ currentTime }}</el-descriptions-item>
          <el-descriptions-item label="用户代理">{{ userAgent }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import {
  Check,
  Close,
  School,
  Reading,
  Connection,
  Star,
  DataAnalysis,
  Download,
  Link,
  TrendCharts
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref({
  health: false,
  direct: false
})

const results = ref<Array<{success: boolean, message: string}>>([])
const currentTime = ref('')
const frontendUrl = ref('')
const backendUrl = ref('http://localhost:8000')
const userAgent = ref('')

// 测试图标
const testIcons = ref([
  { name: 'School', component: School },
  { name: 'Reading', component: Reading },
  { name: 'Connection', component: Connection },
  { name: 'Star', component: Star },
  { name: 'DataAnalysis', component: DataAnalysis },
  { name: 'Download', component: Download },
  { name: 'Link', component: Link },
  { name: 'TrendCharts', component: TrendCharts }
])

// 方法
const addResult = (success: boolean, message: string) => {
  results.value.unshift({ success, message })
  if (results.value.length > 10) {
    results.value = results.value.slice(0, 10)
  }
}

const testHealthAPI = async () => {
  loading.value.health = true
  try {
    const response = await fetch('/api/health')
    if (response.ok) {
      const data = await response.json()
      addResult(true, `健康检查成功: ${JSON.stringify(data)}`)
    } else {
      addResult(false, `健康检查失败: ${response.status} ${response.statusText}`)
    }
  } catch (error: any) {
    addResult(false, `健康检查错误: ${error.message}`)
  } finally {
    loading.value.health = false
  }
}

const testDirectAPI = async () => {
  loading.value.direct = true
  try {
    const response = await fetch('http://localhost:8000/health')
    if (response.ok) {
      const data = await response.json()
      addResult(true, `直连API成功: ${JSON.stringify(data)}`)
    } else {
      addResult(false, `直连API失败: ${response.status} ${response.statusText}`)
    }
  } catch (error: any) {
    addResult(false, `直连API错误: ${error.message}`)
  } finally {
    loading.value.direct = false
  }
}

const showMessage = () => {
  ElMessage.success('Element Plus 消息组件工作正常！')
}

const showNotification = () => {
  ElNotification({
    title: '测试通知',
    message: 'Element Plus 通知组件工作正常！',
    type: 'success'
  })
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const updateNetworkInfo = () => {
  frontendUrl.value = window.location.origin
  userAgent.value = navigator.userAgent.substring(0, 50) + '...'
}

// 定时器
let timeInterval: number

onMounted(() => {
  updateNetworkInfo()
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  
  h3 {
    margin-bottom: 15px;
    color: #303133;
  }
  
  h4 {
    margin: 15px 0 10px 0;
    color: #606266;
  }
}

.results {
  margin-top: 20px;
  
  .result-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-size: 14px;
    
    &.success {
      background-color: #f0f9ff;
      color: #067f23;
      border: 1px solid #b3e5fc;
    }
    
    &.error {
      background-color: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }
  }
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  
  .icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
      background-color: #f5f7fa;
    }
    
    span {
      font-size: 12px;
      color: #606266;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}
</style>
