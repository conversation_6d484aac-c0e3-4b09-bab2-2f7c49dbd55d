<template>
  <div class="data-management-page">
    <div class="page-header">
      <h1>📁 数据管理</h1>
      <p>高考录取数据的查看、编辑和管理</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card mb-20">
      <template #header>
        <div class="card-header">
          <span>数据筛选</span>
          <div class="header-actions">
            <el-button type="primary" :icon="Plus" @click="showImportDialog = true">
              导入数据
            </el-button>
            <el-button :icon="Download" @click="exportData">
              导出数据
            </el-button>
          </div>
        </div>
      </template>
      <el-form :model="searchForm" inline>
        <el-form-item label="学校名称">
          <el-input 
            v-model="searchForm.school_name" 
            placeholder="请输入学校名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="专业名称">
          <el-input 
            v-model="searchForm.major_name" 
            placeholder="请输入专业名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="年份">
          <el-select v-model="searchForm.year" placeholder="选择年份" clearable style="width: 120px">
            <el-option label="2024" value="2024" />
            <el-option label="2023" value="2023" />
            <el-option label="2022" value="2022" />
          </el-select>
        </el-form-item>
        <el-form-item label="科类">
          <el-select v-model="searchForm.science_type" placeholder="选择科类" clearable style="width: 120px">
            <el-option label="物理类" value="physics" />
            <el-option label="历史类" value="history" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchData" :loading="loading">
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>数据列表 (共 {{ pagination.total }} 条)</span>
          <div class="header-actions">
            <el-button 
              type="danger" 
              :icon="Delete" 
              @click="batchDelete"
              :disabled="selectedRows.length === 0"
              size="small"
            >
              批量删除 ({{ selectedRows.length }})
            </el-button>
            <el-button :icon="Refresh" @click="loadData" :loading="loading" size="small">
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="tableData" 
        v-loading="loading"
        stripe
        style="width: 100%"
        empty-text="暂无数据"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="school_name" label="学校名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="major_name" label="专业名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="year" label="年份" width="80" />
        <el-table-column prop="science_type" label="科类" width="80">
          <template #default="{ row }">
            <el-tag :type="row.science_type === 'physics' ? 'primary' : 'success'" size="small">
              {{ row.science_type === 'physics' ? '物理' : '历史' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="province" label="省份" width="100" />
        <el-table-column prop="batch" label="批次" width="100" />
        <el-table-column prop="min_score" label="最低分" width="100" />
        <el-table-column prop="avg_score" label="平均分" width="100" />
        <el-table-column prop="max_score" label="最高分" width="100" />
        <el-table-column prop="enrollment_count" label="录取人数" width="100" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editRecord(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteRecord(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[20, 50, 100, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 导入数据对话框 -->
    <el-dialog v-model="showImportDialog" title="导入数据" width="600px">
      <div class="import-container">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls,.csv"
          drag
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 Excel (.xlsx, .xls) 和 CSV 文件，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
        
        <div v-if="uploadFile" class="file-info">
          <p><strong>选择的文件:</strong> {{ uploadFile.name }}</p>
          <p><strong>文件大小:</strong> {{ (uploadFile.size / 1024 / 1024).toFixed(2) }} MB</p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="importData"
          :loading="importLoading"
          :disabled="!uploadFile"
        >
          开始导入
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑记录对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑记录" width="600px">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="学校名称">
          <el-input v-model="editForm.school_name" />
        </el-form-item>
        <el-form-item label="专业名称">
          <el-input v-model="editForm.major_name" />
        </el-form-item>
        <el-form-item label="年份">
          <el-select v-model="editForm.year" style="width: 100%">
            <el-option label="2024" value="2024" />
            <el-option label="2023" value="2023" />
            <el-option label="2022" value="2022" />
          </el-select>
        </el-form-item>
        <el-form-item label="科类">
          <el-select v-model="editForm.science_type" style="width: 100%">
            <el-option label="物理类" value="physics" />
            <el-option label="历史类" value="history" />
          </el-select>
        </el-form-item>
        <el-form-item label="最低分">
          <el-input-number v-model="editForm.min_score" :min="0" :max="750" />
        </el-form-item>
        <el-form-item label="平均分">
          <el-input-number v-model="editForm.avg_score" :min="0" :max="750" />
        </el-form-item>
        <el-form-item label="最高分">
          <el-input-number v-model="editForm.max_score" :min="0" :max="750" />
        </el-form-item>
        <el-form-item label="录取人数">
          <el-input-number v-model="editForm.enrollment_count" :min="0" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEdit" :loading="editLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  Delete,
  Refresh,
  UploadFilled
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const importLoading = ref(false)
const editLoading = ref(false)
const showImportDialog = ref(false)
const showEditDialog = ref(false)
const uploadFile = ref<File | null>(null)
const selectedRows = ref([])

const searchForm = ref({
  school_name: '',
  major_name: '',
  year: '',
  science_type: ''
})

const pagination = ref({
  current: 1,
  size: 20,
  total: 0
})

const editForm = ref({
  id: null,
  school_name: '',
  major_name: '',
  year: '',
  science_type: '',
  min_score: 0,
  avg_score: 0,
  max_score: 0,
  enrollment_count: 0
})

const tableData = ref([
  {
    id: 1,
    school_name: '清华大学',
    major_name: '计算机科学与技术',
    year: '2024',
    science_type: 'physics',
    province: '重庆',
    batch: '本科一批',
    min_score: 685,
    avg_score: 692,
    max_score: 698,
    enrollment_count: 45
  },
  {
    id: 2,
    school_name: '北京大学',
    major_name: '软件工程',
    year: '2024',
    science_type: 'physics',
    province: '重庆',
    batch: '本科一批',
    min_score: 680,
    avg_score: 687,
    max_score: 695,
    enrollment_count: 38
  }
])

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取数据
    // const response = await dataApi.getData({
    //   ...searchForm.value,
    //   page: pagination.value.current,
    //   size: pagination.value.size
    // })
    // tableData.value = response.data
    // pagination.value.total = response.total
    
    await new Promise(resolve => setTimeout(resolve, 500))
    pagination.value.total = 156
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const searchData = () => {
  pagination.value.current = 1
  loadData()
}

const resetSearch = () => {
  searchForm.value = {
    school_name: '',
    major_name: '',
    year: '',
    science_type: ''
  }
  searchData()
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const editRecord = (row: any) => {
  editForm.value = { ...row }
  showEditDialog.value = true
}

const saveEdit = async () => {
  editLoading.value = true
  try {
    // 这里应该调用API保存编辑
    // await dataApi.updateRecord(editForm.value.id, editForm.value)
    
    ElMessage.success('保存成功')
    showEditDialog.value = false
    await loadData()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    editLoading.value = false
  }
}

const deleteRecord = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 这里应该调用API删除记录
    // await dataApi.deleteRecord(id)
    
    ElMessage.success('删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API批量删除
    // const ids = selectedRows.value.map(row => row.id)
    // await dataApi.batchDelete(ids)
    
    ElMessage.success('批量删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleFileChange = (file: any) => {
  uploadFile.value = file.raw
}

const importData = async () => {
  if (!uploadFile.value) return
  
  importLoading.value = true
  try {
    // 这里应该调用API导入数据
    // const formData = new FormData()
    // formData.append('file', uploadFile.value)
    // await dataApi.importData(formData)
    
    ElMessage.success('数据导入成功')
    showImportDialog.value = false
    uploadFile.value = null
    await loadData()
  } catch (error) {
    ElMessage.error('数据导入失败')
  } finally {
    importLoading.value = false
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSizeChange = (size: number) => {
  pagination.value.size = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.value.current = page
  loadData()
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.data-management-page {
  .page-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 28px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }

  .import-container {
    .file-info {
      margin-top: 16px;
      padding: 12px;
      background: #f5f7fa;
      border-radius: 4px;
      
      p {
        margin: 4px 0;
        font-size: 14px;
      }
    }
  }
}
</style>
