<template>
  <div class="system-page">
    <div class="page-header">
      <h1>系统管理</h1>
      <p>系统配置、监控和维护管理</p>
    </div>

    <!-- 系统状态 -->
    <el-card class="mb-20">
      <template #header>
        <div class="card-header">
          <span>系统状态</span>
          <el-button size="small" @click="refreshSystemStatus">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="status-item">
            <div class="status-label">数据库</div>
            <el-tag :type="systemStatus.database === 'ok' ? 'success' : 'danger'">
              {{ systemStatus.database === 'ok' ? '正常' : '异常' }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="status-label">Redis缓存</div>
            <el-tag :type="systemStatus.redis === 'ok' ? 'success' : 'danger'">
              {{ systemStatus.redis === 'ok' ? '正常' : '异常' }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="status-label">CPU使用率</div>
            <el-progress 
              :percentage="systemStatus.system?.cpu_percent || 0" 
              :stroke-width="8"
            />
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="status-label">内存使用率</div>
            <el-progress 
              :percentage="systemStatus.system?.memory_percent || 0" 
              :stroke-width="8"
            />
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-row :gutter="20">
      <!-- 系统配置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统配置</span>
              <el-button size="small" type="primary" @click="showConfigDialog = true">
                新增配置
              </el-button>
            </div>
          </template>
          
          <el-table :data="configs" stripe max-height="400">
            <el-table-column prop="key" label="配置键" width="150" />
            <el-table-column prop="value" label="配置值" width="120" />
            <el-table-column prop="description" label="描述" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" @click="editConfig(row)">
                  编辑
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="deleteConfig(row)"
                  :disabled="!row.is_editable"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 备份管理 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>备份管理</span>
              <el-button size="small" type="primary" @click="createBackup">
                创建备份
              </el-button>
            </div>
          </template>
          
          <el-table :data="backups" stripe max-height="400">
            <el-table-column prop="backup_name" label="备份名称" />
            <el-table-column prop="file_size_human" label="文件大小" width="100" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getBackupStatusType(row.status)" size="small">
                  {{ getBackupStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="120">
              <template #default="{ row }">
                {{ formatTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row }">
                <el-button size="small" @click="downloadBackup(row)">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统日志 -->
    <el-card class="mt-20">
      <template #header>
        <div class="card-header">
          <span>系统日志</span>
          <div class="header-controls">
            <el-select v-model="logLevel" placeholder="日志级别" clearable size="small">
              <el-option label="DEBUG" value="DEBUG" />
              <el-option label="INFO" value="INFO" />
              <el-option label="WARNING" value="WARNING" />
              <el-option label="ERROR" value="ERROR" />
            </el-select>
            <el-button size="small" @click="refreshLogs">刷新</el-button>
          </div>
        </div>
      </template>
      
      <div class="logs-container">
        <div 
          v-for="log in logs" 
          :key="log.id"
          :class="['log-item', `log-${log.level.toLowerCase()}`]"
        >
          <span class="log-time">{{ formatTime(log.created_at) }}</span>
          <span class="log-level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>

    <!-- 配置编辑对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      :title="editingConfig ? '编辑配置' : '新增配置'"
      width="600px"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="100px"
      >
        <el-form-item label="配置键" prop="key">
          <el-input 
            v-model="configForm.key" 
            placeholder="请输入配置键"
            :disabled="!!editingConfig"
          />
        </el-form-item>
        <el-form-item label="配置值" prop="value">
          <el-input v-model="configForm.value" placeholder="请输入配置值" />
        </el-form-item>
        <el-form-item label="数据类型" prop="data_type">
          <el-select v-model="configForm.data_type" style="width: 100%">
            <el-option label="字符串" value="string" />
            <el-option label="整数" value="integer" />
            <el-option label="浮点数" value="float" />
            <el-option label="布尔值" value="boolean" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="configForm.category" placeholder="请输入分类" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="configForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="公开配置">
          <el-switch v-model="configForm.is_public" />
        </el-form-item>
        <el-form-item label="可编辑">
          <el-switch v-model="configForm.is_editable" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelConfigEdit">取消</el-button>
        <el-button 
          type="primary" 
          @click="saveConfig"
          :loading="saveLoading"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const showConfigDialog = ref(false)
const editingConfig = ref(null)
const logLevel = ref('')

const systemStatus = ref({
  database: 'ok',
  redis: 'ok',
  system: {
    cpu_percent: 25,
    memory_percent: 60
  }
})

const configs = ref([])
const backups = ref([])
const logs = ref([])

const configFormRef = ref<FormInstance>()

// 表单数据
const configForm = ref({
  key: '',
  value: '',
  data_type: 'string',
  category: '',
  description: '',
  is_public: false,
  is_editable: true
})

// 表单验证规则
const configRules: FormRules = {
  key: [
    { required: true, message: '请输入配置键', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ],
  data_type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请输入分类', trigger: 'blur' }
  ]
}

// 方法
const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm:ss')
}

const getBackupStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    completed: 'success',
    failed: 'danger',
    running: 'warning'
  }
  return typeMap[status] || 'info'
}

const getBackupStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    completed: '完成',
    failed: '失败',
    running: '进行中'
  }
  return textMap[status] || status
}

const refreshSystemStatus = async () => {
  try {
    // 这里应该调用API获取系统状态
    // const response = await systemApi.getStatus()
    // systemStatus.value = response.data
    ElMessage.success('系统状态刷新成功')
  } catch (error) {
    ElMessage.error('刷新系统状态失败')
  }
}

const loadConfigs = async () => {
  try {
    // 这里应该调用API获取配置列表
    // const response = await systemApi.getConfigs()
    
    // 模拟数据
    configs.value = [
      {
        id: 1,
        key: 'app_name',
        value: '高考数据管理平台',
        data_type: 'string',
        category: 'app',
        description: '应用名称',
        is_public: true,
        is_editable: true
      },
      {
        id: 2,
        key: 'max_file_size',
        value: '10MB',
        data_type: 'string',
        category: 'upload',
        description: '最大文件上传大小',
        is_public: false,
        is_editable: true
      }
    ]
  } catch (error) {
    ElMessage.error('加载配置失败')
  }
}

const loadBackups = async () => {
  try {
    // 这里应该调用API获取备份列表
    // const response = await systemApi.getBackups()
    
    // 模拟数据
    backups.value = [
      {
        id: 1,
        backup_name: '自动备份_20240320',
        file_size_human: '2.3GB',
        status: 'completed',
        created_at: '2024-03-20T02:00:00Z'
      }
    ]
  } catch (error) {
    ElMessage.error('加载备份列表失败')
  }
}

const loadLogs = async () => {
  try {
    // 这里应该调用API获取日志
    // const response = await systemApi.getLogs({ level: logLevel.value })
    
    // 模拟数据
    logs.value = [
      {
        id: 1,
        level: 'INFO',
        message: '系统启动成功',
        created_at: '2024-03-20T10:30:00Z'
      },
      {
        id: 2,
        level: 'WARNING',
        message: '内存使用率较高',
        created_at: '2024-03-20T10:25:00Z'
      }
    ]
  } catch (error) {
    ElMessage.error('加载日志失败')
  }
}

const editConfig = (config: any) => {
  editingConfig.value = config
  configForm.value = { ...config }
  showConfigDialog.value = true
}

const saveConfig = async () => {
  if (!configFormRef.value) return
  
  const valid = await configFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  saveLoading.value = true
  try {
    if (editingConfig.value) {
      // 更新配置
      // await systemApi.updateConfig(editingConfig.value.id, configForm.value)
      ElMessage.success('配置更新成功')
    } else {
      // 创建配置
      // await systemApi.createConfig(configForm.value)
      ElMessage.success('配置创建成功')
    }
    
    showConfigDialog.value = false
    await loadConfigs()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

const cancelConfigEdit = () => {
  showConfigDialog.value = false
  editingConfig.value = null
  configForm.value = {
    key: '',
    value: '',
    data_type: 'string',
    category: '',
    description: '',
    is_public: false,
    is_editable: true
  }
}

const deleteConfig = async (config: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置 ${config.key} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用API删除配置
    // await systemApi.deleteConfig(config.id)
    
    ElMessage.success('配置删除成功')
    await loadConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const createBackup = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要创建系统备份吗？此操作可能需要较长时间。',
      '确认备份',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 这里应该调用API创建备份
    // await systemApi.createBackup({ backup_name: `手动备份_${dayjs().format('YYYYMMDD_HHmmss')}` })
    
    ElMessage.success('备份任务已创建')
    await loadBackups()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('创建备份失败')
    }
  }
}

const downloadBackup = (backup: any) => {
  ElMessage.info('下载功能开发中...')
}

const refreshLogs = () => {
  loadLogs()
}

// 生命周期
onMounted(() => {
  refreshSystemStatus()
  loadConfigs()
  loadBackups()
  loadLogs()
})
</script>

<style lang="scss" scoped>
.system-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    p {
      color: #909399;
      margin: 0;
    }
  }
  
  .status-item {
    text-align: center;
    
    .status-label {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    
    .header-controls {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }
  
  .logs-container {
    height: 300px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 4px;
    padding: 12px;
    
    .log-item {
      display: flex;
      gap: 12px;
      padding: 6px 0;
      border-bottom: 1px solid #eee;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      
      &:last-child {
        border-bottom: none;
      }
      
      .log-time {
        color: #909399;
        white-space: nowrap;
        width: 100px;
      }
      
      .log-level {
        font-weight: 600;
        width: 60px;
        text-align: center;
        
        &.log-info { color: #67c23a; }
        &.log-warning { color: #e6a23c; }
        &.log-error { color: #f56c6c; }
      }
      
      .log-message {
        flex: 1;
        word-break: break-all;
      }
    }
  }
}
</style>
