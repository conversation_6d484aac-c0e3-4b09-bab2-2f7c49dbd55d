<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔗 前后端连接测试</h1>
    
    <div class="test-container">
        <h2>📡 API连接测试</h2>
        <button onclick="testDirectAPI()">测试直连API (8000端口)</button>
        <button onclick="testProxyAPI()">测试代理API (3000端口)</button>
        <button onclick="testHealthCheck()">健康检查</button>
        <div id="api-result"></div>
    </div>

    <div class="test-container">
        <h2>🌐 网络状态</h2>
        <div id="network-status">
            <p><strong>前端地址:</strong> <span id="frontend-url"></span></p>
            <p><strong>后端地址:</strong> <span id="backend-url"></span></p>
            <p><strong>当前时间:</strong> <span id="current-time"></span></p>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        // 更新网络状态
        function updateNetworkStatus() {
            document.getElementById('frontend-url').textContent = window.location.origin;
            document.getElementById('backend-url').textContent = 'http://localhost:8000';
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }

        // 显示结果
        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        // 测试直连API
        async function testDirectAPI() {
            const resultContainer = 'api-result';
            showResult(resultContainer, '🔄 正在测试直连API...', 'loading');
            
            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                
                if (response.ok) {
                    showResult(resultContainer, `✅ 直连API成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    showResult(resultContainer, `❌ 直连API失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult(resultContainer, `❌ 直连API错误: ${error.message}`, 'error');
            }
        }

        // 测试代理API
        async function testProxyAPI() {
            const resultContainer = 'api-result';
            showResult(resultContainer, '🔄 正在测试代理API...', 'loading');
            
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    showResult(resultContainer, `✅ 代理API成功: ${JSON.stringify(data)}`, 'success');
                } else {
                    showResult(resultContainer, `❌ 代理API失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult(resultContainer, `❌ 代理API错误: ${error.message}`, 'error');
            }
        }

        // 健康检查
        async function testHealthCheck() {
            const resultContainer = 'test-results';
            showResult(resultContainer, '🔄 正在进行健康检查...', 'loading');
            
            const tests = [
                { name: '后端服务', url: 'http://localhost:8000/health' },
                { name: '代理服务', url: '/api/health' },
                { name: 'API文档', url: 'http://localhost:8000/docs' }
            ];

            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    if (response.ok) {
                        showResult(resultContainer, `✅ ${test.name}: 正常`, 'success');
                    } else {
                        showResult(resultContainer, `❌ ${test.name}: ${response.status}`, 'error');
                    }
                } catch (error) {
                    showResult(resultContainer, `❌ ${test.name}: ${error.message}`, 'error');
                }
            }
        }

        // 页面加载时更新状态
        updateNetworkStatus();
        setInterval(updateNetworkStatus, 1000);
    </script>
</body>
</html>
