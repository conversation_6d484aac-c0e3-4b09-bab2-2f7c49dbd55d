{"name": "gaokao-frontend", "version": "1.0.0", "description": "高考数据管理平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.9.0", "axios": "^1.6.8", "dayjs": "^1.11.10", "echarts": "^5.5.0", "element-plus": "^2.6.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-echarts": "^6.7.3", "vue-router": "^4.3.0"}, "devDependencies": {"@types/node": "^20.11.30", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.24.0", "prettier": "^3.2.5", "sass-embedded": "^1.89.2", "typescript": "^5.4.2", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.6", "vue-tsc": "^2.0.7"}}