## 高考数据收集分析平台

> 基于提供的API文档设计的高考院校专业数据收集、存储、分析和可视化展示平台

> 为高考志愿填报提供数据支持，帮助学生和家长做出更明智的选择

> ✅ 已完成 - 完整的爬虫管理系统，支持实时监控、任务控制、断点续传等功能

> 个人项目

> Python + FastAPI + WebSocket + Vue3 + TypeScript + Element Plus + SQLite

## Dependencies

### 后端依赖 (requirements.txt)
* fastapi (0.104.1): 高性能异步Web框架，用于构建API服务
* tortoise-orm (0.20.0): 异步ORM框架，用于数据库操作
* httpx (0.25.2): 异步HTTP客户端，用于爬虫数据收集
* aiosqlite (0.19.0): SQLite异步驱动
* redis (5.0.1): Redis客户端，用于缓存和任务状态管理
* websockets (12.0): WebSocket支持，用于实时通信
* pandas (2.1.4): 数据分析库
* loguru (0.7.2): 现代化日志库

### 前端依赖 (package.json)
* vue (3.4.21): 渐进式JavaScript框架
* element-plus (2.6.1): Vue3组件库
* typescript (5.4.2): TypeScript支持
* pinia (2.1.7): Vue状态管理
* vue-router (4.3.0): Vue路由管理

## Development Environment

### 环境要求
* Python 3.11+
* SQLite 3+ (自动创建)
* Redis 6+ (可选，用于缓存)
* Node.js 16+ (前端)

### 启动方式
1. 后端: `python backend/app/simple_main.py` 或 `cd backend && python app/simple_main.py`
2. 前端: `cd frontend && npm run dev` 或 `cd frontend && pnpm dev`
3. 访问: http://localhost:3000 (前端) 和 http://localhost:8000/docs (API文档)

### 核心功能
* ✅ 实时爬虫任务管理 - 创建、启动、暂停、恢复、取消任务
* ✅ WebSocket实时通信 - 实时日志推送和状态更新
* ✅ 可视化操作界面 - 完整的前端管理系统
* ✅ 断点续传机制 - 任务中断后可继续执行
* ✅ 数据完整性保护 - 自动处理重复数据和异常情况
* ✅ 进度监控 - 实时显示爬取进度和统计信息
* ✅ API接口修复 - 修复前后端API路径不匹配问题
* ✅ 任务状态管理 - 修复任务状态枚举转换问题
* ✅ 完整测试覆盖 - 包含爬虫功能、WebSocket、任务管理的全面测试
* ✅ 数据分析可视化 - 多维度统计分析和ECharts图表展示
* ✅ 性能优化 - 数据库连接池、Redis缓存、API限流
* ✅ 安全加固 - 输入验证、错误处理、安全中间件
* ✅ 统一配置管理 - 环境变量统一管理和类型安全验证
* ✅ 爬虫配置可视化编辑 - 支持爬虫参数、请求头、代理设置的可视化配置
* ✅ 爬虫调度管理 - 支持定时任务、执行频率、时间窗口等调度功能
* ✅ 增强版用户界面 - 现代化设计、响应式布局、多视图切换
* ✅ 任务管理优化 - 改进任务创建、编辑、删除和状态跟踪功能
* ✅ 爬虫管理界面全面优化 - API对接修复、现代化UI设计、完整任务控制功能
* ✅ 现代化设计系统 - 简洁清新的界面风格、统一的色彩系统、流畅的动画效果
* ✅ 完整任务控制 - 暂停、恢复、取消、删除等全生命周期管理
* ✅ 实时日志优化 - 改进的日志显示、级别筛选、搜索功能
* ✅ 独立异步爬虫系统 - 基于API文档开发的完整异步爬虫，支持获取全国高校2022-2024年专业录取数据
* ✅ 企业级爬虫功能 - 断点续传、分批保存、完整日志记录、错误处理机制
* ✅ 演示模式 - 提供完整的演示系统，展示爬虫架构和功能特性
* ✅ 配置管理系统 - 支持环境变量配置、动态token管理、灵活的参数调整
* ✅ 前后端对接修复 - 修复端口配置不匹配、代理连接问题，确保前后端正常通信
* ✅ Element Plus图标规范化 - 统一使用官方图标，修复所有组件中的图标引用错误
* ✅ 完整功能模块 - 爬虫管理、数据分析、数据管理、系统管理四大核心模块
* ✅ 现代化UI组件库 - SystemStatus、CrawlerTaskManager、RealTimeLog等专业组件
* ✅ 响应式设计优化 - 适配不同屏幕尺寸，提供优秀的用户体验

## Structure

```
gaokao/
├── backend/                          // 后端服务目录
│   ├── app/                         // 应用核心代码
│   │   ├── models/                  // 数据模型层 - 基于Tortoise ORM设计
│   │   │   ├── __init__.py         // 模型导出文件
│   │   │   ├── base.py             // 基础模型：Province、Batch、ScienceType等
│   │   │   ├── university.py       // 大学模型：对应API中的学校信息
│   │   │   ├── major.py            // 专业模型：对应API中的专业信息
│   │   │   ├── admission.py        // 录取记录模型：AdmissionRecord和MajorAdmissionRecord
│   │   │   └── system.py           // 系统模型：CrawlLog、DataUpdateStatus等
│   │   ├── crawler/                 // 爬虫模块 - 核心数据收集功能
│   │   │   ├── __init__.py         // 爬虫模块导出
│   │   │   ├── config.py           // 爬虫配置：API端点、请求参数、并发控制等
│   │   │   ├── spider.py           // 爬虫核心类：GaokaoSpider，实现异步数据爬取
│   │   │   └── data_processor.py   // 数据处理器：原始数据清洗和转换为数据库模型
│   │   ├── api/                     // API接口层
│   │   │   ├── crawler.py          // 爬虫管理API：任务创建、状态查询等
│   │   │   ├── websocket.py        // WebSocket API：实时通信接口
│   │   │   ├── analytics.py        // 数据分析API：统计分析和可视化数据接口
│   │   │   └── system.py           // 系统API：健康检查、统计信息等
│   │   ├── services/                // 业务服务层
│   │   │   ├── task_manager.py     // 任务管理服务：任务控制、状态管理、进度跟踪
│   │   │   ├── cache_service.py    // 缓存服务：Redis缓存管理和装饰器
│   │   │   └── analytics_service.py // 数据分析服务：多维度统计分析
│   │   ├── middleware/              // 中间件层
│   │   │   └── security.py         // 安全中间件：限流、认证、安全头、日志
│   │   ├── websocket.py             // WebSocket管理：连接管理、消息广播、日志推送
│   │   ├── core/                    // 核心配置
│   │   │   ├── database.py         // 数据库配置：Tortoise ORM配置和Redis连接
│   │   │   ├── config.py           // 统一配置管理：环境变量和应用配置
│   │   │   └── exceptions.py       // 全局异常处理：统一错误处理和响应格式
│   │   └── main.py                 // FastAPI应用入口
│   ├── requirements.txt             // Python依赖包列表
│   ├── test_simple.py              // 简单爬虫测试
│   ├── debug_task.py               // 任务调试脚本
│   ├── final_test.py               // 完整系统测试
│   ├── test_crawler_functionality.py // 爬虫功能测试
│   ├── test_websocket_task.py      // WebSocket和任务管理测试
│   └── test_full_crawler_flow.py   // 完整爬虫流程测试
├── frontend/                        // 前端项目目录 - Vue3 + TypeScript
│   ├── src/                         // 源代码目录
│   │   ├── components/              // Vue组件
│   │   │   ├── SystemStatus.vue    // 系统状态组件：服务状态、资源监控、任务统计
│   │   │   ├── CrawlerTaskManager.vue // 任务管理组件：任务列表、控制操作、进度显示
│   │   │   ├── RealTimeLog.vue     // 实时日志组件：WebSocket日志流、级别筛选、搜索功能
│   │   │   ├── CreateTaskDialog.vue // 创建任务对话框：任务配置、高级选项、预估信息
│   │   │   └── DataVisualization.vue // 数据可视化组件：ECharts图表封装
│   │   ├── views/                   // 页面视图
│   │   │   ├── Dashboard.vue       // 仪表板主页面：系统概览和快速操作
│   │   │   ├── Crawler.vue         // 爬虫管理主页面：集成系统状态、任务管理、实时日志
│   │   │   ├── Analytics.vue       // 数据分析页面：筛选条件、统计图表、详细数据
│   │   │   ├── DataManagement.vue  // 数据管理页面：数据查询、批量操作、导入导出
│   │   │   ├── System.vue          // 系统管理页面：配置管理、备份管理、系统监控
│   │   │   └── Test.vue            // 系统测试页面：API连接测试、组件测试、网络信息
│   │   ├── stores/                  // Pinia状态管理
│   │   │   └── crawler.ts          // 爬虫状态管理
│   │   ├── services/                // 服务层
│   │   │   └── websocket.ts        // WebSocket服务
│   │   ├── api/                     // API接口
│   │   │   ├── crawler.ts          // 爬虫API
│   │   │   ├── universities.ts     // 大学API
│   │   │   ├── analytics.ts        // 数据分析API：统计分析接口封装
│   │   │   ├── system.ts           // 系统API
│   │   │   ├── request.ts          // 请求封装
│   │   │   └── index.ts            // API基础配置
│   │   └── main.ts                 // 应用入口
│   ├── package.json                 // 前端依赖配置
│   ├── vite.config.ts              // Vite构建配置
│   └── test_api_endpoints.html     // API端点测试工具
├── spider/                          // 独立异步爬虫系统目录
│   ├── __init__.py                 // 爬虫包初始化文件
│   ├── config.py                   // 配置管理模块：API端点、认证信息、请求参数等
│   ├── logger.py                   // 日志管理模块：统一日志记录功能
│   ├── progress_manager.py         // 进度管理模块：断点续传功能实现
│   ├── data_processor.py           // 数据处理模块：Excel输出和数据转换
│   ├── crawler.py                  // 异步爬虫核心模块：主要爬取逻辑
│   ├── main.py                     // 主程序入口：命令行接口和功能入口
│   ├── demo_crawler.py             // 演示模式爬虫：模拟数据展示系统功能
│   ├── test_crawler.py             // 测试脚本：验证系统各组件功能
│   ├── requirements.txt            // 爬虫系统依赖包列表
│   ├── config.env.example          // 配置文件示例：API认证和参数配置
│   ├── README.md                   // 爬虫系统详细说明文档
│   ├── output/                     // Excel文件输出目录
│   └── logs/                       // 日志文件目录
├── demo_run.py                     // 演示运行脚本：独立的演示程序入口
├── 高考数据爬虫系统使用指南.md        // 完整的使用指南文档
├── README.md                       // 项目说明文档：详细的功能介绍和使用指南
├── PROJECT_OPTIMIZATION_REPORT.md // 项目优化报告：详细的优化内容和改进说明
├── docker-compose.yml              // Docker编排配置：完整的容器化部署配置
└── .codelf/                        // 项目元数据目录
    ├── project.md                  // 项目结构说明
    └── changelog.md                // 变更日志
```
