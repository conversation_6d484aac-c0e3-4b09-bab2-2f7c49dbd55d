@echo off
echo 🔧 启动后端服务器...

cd backend

if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
)

echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

echo 📦 安装Python依赖...
pip install -r requirements.txt

if not exist ".env" (
    echo 📝 创建环境配置文件...
    copy .env.example .env
    echo ⚠️  请编辑 backend\.env 文件配置数据库和其他参数
)

echo 🗄️  初始化数据库...
if not exist "migrations" (
    aerich init -t app.core.database.TORTOISE_ORM
    aerich init-db
) else (
    aerich upgrade
)

echo 🚀 启动后端服务 (http://localhost:8000)...
python run.py

pause
