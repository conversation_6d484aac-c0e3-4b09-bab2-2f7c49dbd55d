# 数据库配置
DATABASE_URL=sqlite://./gaokao.db
# 生产环境可使用 PostgreSQL
# DATABASE_URL=postgresql://user:password@localhost:5432/gaokao

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT认证配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 应用配置
APP_NAME=高考数据管理平台
APP_VERSION=1.0.0
DEBUG=true
API_PREFIX=/api/v1

# 爬虫配置
CRAWLER_API_BASE_URL=https://applet.cqzk.com.cn/prod/history/front/history
CRAWLER_API_SECRET=your-api-secret-here
CRAWLER_MAX_CONCURRENT=10
CRAWLER_REQUEST_DELAY=1
CRAWLER_MAX_RETRIES=3
CRAWLER_REQUEST_TIMEOUT=30

# 安全配置
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
RATE_LIMIT_PER_MINUTE=60

# 文件上传配置
MAX_FILE_SIZE=50MB
UPLOAD_DIR=./uploads

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
