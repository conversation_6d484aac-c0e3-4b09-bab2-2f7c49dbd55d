"""
简化版后端启动脚本 - 跳过数据库初始化
"""
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 创建简化的FastAPI应用
app = FastAPI(
    title="高考数据管理平台",
    description="智能化高考数据收集、分析和推荐系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "高考数据管理平台API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "message": "服务正常运行"}

@app.get("/api/v1/test")
async def test_api():
    """测试API"""
    return {"message": "API测试成功", "data": {"test": True}}

# 认证相关API
@app.post("/api/v1/auth/login")
async def login():
    """用户登录"""
    return {
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "token_type": "bearer"
    }

@app.get("/api/v1/auth/me")
async def get_current_user():
    """获取当前用户信息"""
    return {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "系统管理员",
        "role": "super_admin",
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z"
    }

@app.get("/api/v1/auth/permissions")
async def get_user_permissions():
    """获取用户权限"""
    return {
        "permissions": [
            "dashboard:view",
            "crawler:view", "crawler:create", "crawler:update", "crawler:delete",
            "university:view", "university:create", "university:update", "university:delete",
            "major:view", "major:create", "major:update", "major:delete",
            "recommendation:view", "recommendation:create",
            "analytics:view",
            "user:view", "user:create", "user:update", "user:delete",
            "system:view", "system:update"
        ]
    }

if __name__ == "__main__":
    print("🚀 启动简化版高考数据管理平台...")
    print("📱 前端地址: http://localhost:3000")
    print("🔧 后端地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print()
    
    uvicorn.run(
        "run_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
