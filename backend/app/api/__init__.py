"""
API路由包
"""
from fastapi import APIRouter
from .auth import router as auth_router
from .users import router as users_router
from .recommendations import router as recommendations_router
from .universities import router as universities_router
from .majors import router as majors_router
from .analytics import router as analytics_router
from .system import router as system_router
from .crawler import router as crawler_router

# 创建主路由
api_router = APIRouter()

# 注册子路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(users_router, prefix="/users", tags=["用户管理"])
api_router.include_router(crawler_router, prefix="/crawler", tags=["爬虫管理"])
api_router.include_router(recommendations_router, prefix="/recommendations", tags=["专业推荐"])
api_router.include_router(universities_router, prefix="/universities", tags=["大学管理"])
api_router.include_router(majors_router, prefix="/majors", tags=["专业管理"])
api_router.include_router(analytics_router, prefix="/analytics", tags=["数据分析"])
api_router.include_router(system_router, prefix="/system", tags=["系统管理"])

__all__ = ["api_router"]
