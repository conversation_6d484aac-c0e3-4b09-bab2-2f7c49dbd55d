"""
数据分析相关API
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel, ConfigDict

from ..core.security import get_current_active_user, require_permission
from ..models.user import User, Permission
from ..models.university import University, Province, ScienceType
from ..models.major import Major, MajorCategory
from ..models.admission import AdmissionRecord

router = APIRouter()


# 响应模型
class OverviewStats(BaseModel):
    total_universities: int
    total_majors: int
    total_admission_records: int
    universities_985: int
    universities_211: int
    latest_year: int


class TrendData(BaseModel):
    year: int
    value: float
    count: int


class RankingItem(BaseModel):
    name: str
    value: float
    count: int
    extra_info: Dict[str, Any]


@router.get("/overview", response_model=OverviewStats, summary="获取数据概览")
async def get_overview_stats(
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取数据概览统计"""
    total_universities = await University.all().count()
    total_majors = await Major.all().count()
    total_admission_records = await AdmissionRecord.all().count()
    universities_985 = await University.filter(is_985=True).count()
    universities_211 = await University.filter(is_211=True).count()
    
    # 获取最新年份
    latest_record = await AdmissionRecord.all().order_by("-year").first()
    latest_year = latest_record.year if latest_record else 2024
    
    return OverviewStats(
        total_universities=total_universities,
        total_majors=total_majors,
        total_admission_records=total_admission_records,
        universities_985=universities_985,
        universities_211=universities_211,
        latest_year=latest_year
    )


@router.get("/trends/admission-scores", response_model=List[TrendData], summary="录取分数趋势")
async def get_admission_score_trends(
    science_type: ScienceType = Query(..., description="科类"),
    province: Optional[Province] = Query(None, description="省份"),
    university_id: Optional[int] = Query(None, description="大学ID"),
    major_id: Optional[int] = Query(None, description="专业ID"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取录取分数趋势数据"""
    query = AdmissionRecord.filter(science_type=science_type)
    
    if province:
        query = query.filter(province=province.value)
    
    if university_id:
        query = query.filter(university_id=university_id)
    
    if major_id:
        query = query.filter(major_id=major_id)
    
    # 按年份分组统计
    records = await query.all()
    
    year_stats = {}
    for record in records:
        year = record.year
        if year not in year_stats:
            year_stats[year] = {"total_score": 0, "count": 0}
        
        if record.avg_score:
            year_stats[year]["total_score"] += record.avg_score
            year_stats[year]["count"] += 1
    
    trends = []
    for year, stats in sorted(year_stats.items()):
        if stats["count"] > 0:
            avg_score = stats["total_score"] / stats["count"]
            trends.append(TrendData(
                year=year,
                value=round(avg_score, 2),
                count=stats["count"]
            ))
    
    return trends


@router.get("/rankings/universities", response_model=List[RankingItem], summary="大学排名")
async def get_university_rankings(
    science_type: ScienceType = Query(..., description="科类"),
    year: int = Query(2024, description="年份"),
    province: Optional[Province] = Query(None, description="省份"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取大学排名（按平均录取分数）"""
    query = AdmissionRecord.filter(
        science_type=science_type,
        year=year
    ).select_related("university")
    
    if province:
        query = query.filter(university__province=province)
    
    records = await query.all()
    
    # 按大学分组计算平均分
    university_stats = {}
    for record in records:
        uni_id = record.university.id
        uni_name = record.university.name
        
        if uni_id not in university_stats:
            university_stats[uni_id] = {
                "name": uni_name,
                "total_score": 0,
                "count": 0,
                "is_985": record.university.is_985,
                "is_211": record.university.is_211,
                "province": record.university.province.value
            }
        
        if record.avg_score:
            university_stats[uni_id]["total_score"] += record.avg_score
            university_stats[uni_id]["count"] += 1
    
    # 计算平均分并排序
    rankings = []
    for uni_id, stats in university_stats.items():
        if stats["count"] > 0:
            avg_score = stats["total_score"] / stats["count"]
            rankings.append(RankingItem(
                name=stats["name"],
                value=round(avg_score, 2),
                count=stats["count"],
                extra_info={
                    "is_985": stats["is_985"],
                    "is_211": stats["is_211"],
                    "province": stats["province"]
                }
            ))
    
    # 按平均分排序
    rankings.sort(key=lambda x: x.value, reverse=True)
    
    return rankings[:limit]


@router.get("/rankings/majors", response_model=List[RankingItem], summary="专业热度排名")
async def get_major_popularity_rankings(
    category: Optional[MajorCategory] = Query(None, description="专业大类"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取专业热度排名"""
    query = Major.all()
    
    if category:
        query = query.filter(category=category)
    
    majors = await query.order_by("-popularity_score").limit(limit).all()
    
    rankings = []
    for major in majors:
        rankings.append(RankingItem(
            name=major.name,
            value=major.popularity_score,
            count=major.total_enrollments,
            extra_info={
                "category": major.category.value if major.category else None,
                "total_universities": major.total_universities,
                "employment_rate": major.employment_rate,
                "average_salary": major.average_salary
            }
        ))
    
    return rankings


@router.get("/distribution/scores", summary="分数分布分析")
async def get_score_distribution(
    science_type: ScienceType = Query(..., description="科类"),
    year: int = Query(2024, description="年份"),
    province: Optional[Province] = Query(None, description="省份"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取分数分布分析"""
    query = AdmissionRecord.filter(
        science_type=science_type,
        year=year,
        min_score__isnull=False,
        max_score__isnull=False
    )
    
    if province:
        query = query.filter(province=province.value)
    
    records = await query.all()
    
    # 分数区间统计
    score_ranges = {
        "700+": 0,
        "650-699": 0,
        "600-649": 0,
        "550-599": 0,
        "500-549": 0,
        "450-499": 0,
        "400-449": 0,
        "400以下": 0
    }
    
    for record in records:
        min_score = record.min_score
        if min_score >= 700:
            score_ranges["700+"] += 1
        elif min_score >= 650:
            score_ranges["650-699"] += 1
        elif min_score >= 600:
            score_ranges["600-649"] += 1
        elif min_score >= 550:
            score_ranges["550-599"] += 1
        elif min_score >= 500:
            score_ranges["500-549"] += 1
        elif min_score >= 450:
            score_ranges["450-499"] += 1
        elif min_score >= 400:
            score_ranges["400-449"] += 1
        else:
            score_ranges["400以下"] += 1
    
    return {
        "distribution": [
            {"range": range_name, "count": count}
            for range_name, count in score_ranges.items()
        ],
        "total_records": len(records)
    }


@router.get("/competition/provinces", summary="省份竞争分析")
async def get_province_competition_analysis(
    science_type: ScienceType = Query(..., description="科类"),
    year: int = Query(2024, description="年份"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取各省份竞争激烈程度分析"""
    records = await AdmissionRecord.filter(
        science_type=science_type,
        year=year,
        min_score__isnull=False
    ).all()
    
    # 按省份统计
    province_stats = {}
    for record in records:
        province = record.province
        if province not in province_stats:
            province_stats[province] = {
                "total_records": 0,
                "total_score": 0,
                "min_score_sum": 0
            }
        
        province_stats[province]["total_records"] += 1
        if record.avg_score:
            province_stats[province]["total_score"] += record.avg_score
        if record.min_score:
            province_stats[province]["min_score_sum"] += record.min_score
    
    # 计算竞争指数
    competition_data = []
    for province, stats in province_stats.items():
        if stats["total_records"] > 0:
            avg_score = stats["total_score"] / stats["total_records"]
            avg_min_score = stats["min_score_sum"] / stats["total_records"]
            
            # 竞争指数 = 平均最低分 * 录取记录数量权重
            competition_index = avg_min_score * (1 + stats["total_records"] / 1000)
            
            competition_data.append({
                "province": province,
                "competition_index": round(competition_index, 2),
                "avg_score": round(avg_score, 2),
                "avg_min_score": round(avg_min_score, 2),
                "total_records": stats["total_records"]
            })
    
    # 按竞争指数排序
    competition_data.sort(key=lambda x: x["competition_index"], reverse=True)
    
    return {"competition_analysis": competition_data}
