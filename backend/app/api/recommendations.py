"""
专业推荐相关API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field, ConfigDict

from ..core.security import get_current_active_user, require_permission
from ..models.user import User, Permission
from ..models.university import ScienceType
from ..models.recommendation import RecommendationConfig, RecommendationHistory, RecommendationResult
from ..services.recommendation_service import recommendation_engine

router = APIRouter()


# 请求模型
class RecommendationConfigCreate(BaseModel):
    name: str = Field(..., description="配置名称")
    description: Optional[str] = Field(None, description="配置描述")
    
    # 权重配置
    rank_match_weight: float = Field(0.4, ge=0, le=1, description="位次匹配权重")
    school_tier_weight: float = Field(0.3, ge=0, le=1, description="学校层次权重")
    major_popularity_weight: float = Field(0.2, ge=0, le=1, description="专业热度权重")
    location_preference_weight: float = Field(0.1, ge=0, le=1, description="地域偏好权重")
    
    # 推荐比例配置
    rush_ratio: float = Field(0.3, ge=0, le=1, description="冲刺比例")
    stable_ratio: float = Field(0.4, ge=0, le=1, description="稳妥比例")
    safe_ratio: float = Field(0.3, ge=0, le=1, description="保底比例")
    
    # 筛选条件
    preferred_provinces: List[str] = Field(default_factory=list, description="偏好省份")
    excluded_provinces: List[str] = Field(default_factory=list, description="排除省份")
    preferred_school_types: List[str] = Field(default_factory=list, description="偏好学校类型")
    preferred_major_categories: List[str] = Field(default_factory=list, description="偏好专业大类")
    excluded_major_categories: List[str] = Field(default_factory=list, description="排除专业大类")
    
    # 高级配置
    min_score_tolerance: int = Field(1000, ge=0, description="最小分数容差")
    max_recommendations: int = Field(100, ge=1, le=500, description="最大推荐数量")
    enable_985_priority: bool = Field(True, description="985优先")
    enable_211_priority: bool = Field(True, description="211优先")


class RecommendationRequest(BaseModel):
    user_rank: int = Field(..., gt=0, description="用户位次")
    science_type: ScienceType = Field(..., description="科类")
    config_id: Optional[int] = Field(None, description="配置ID，不提供则使用默认配置")
    target_year: int = Field(2024, ge=2020, le=2030, description="目标年份")
    strategy_name: str = Field("standard", description="推荐策略")


# 响应模型
class RecommendationConfigResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: Optional[str]
    rank_match_weight: float
    school_tier_weight: float
    major_popularity_weight: float
    location_preference_weight: float
    rush_ratio: float
    stable_ratio: float
    safe_ratio: float
    preferred_provinces: List[str]
    excluded_provinces: List[str]
    preferred_school_types: List[str]
    preferred_major_categories: List[str]
    excluded_major_categories: List[str]
    min_score_tolerance: int
    max_recommendations: int
    enable_985_priority: bool
    enable_211_priority: bool
    is_default: bool
    is_active: bool
    created_at: str


class RecommendationResultResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    university_name: str
    university_code: str
    major_name: str
    recommendation_type: str
    total_score: float
    rank_match_score: float
    school_tier_score: float
    major_popularity_score: float
    location_preference_score: float
    rank_in_type: int
    overall_rank: int

    # 录取信息
    min_score: Optional[int]
    max_score: Optional[int]
    min_rank: Optional[int]
    max_rank: Optional[int]
    plan_count: Optional[int]
    actual_count: Optional[int]

    # 学校信息
    is_985: bool
    is_211: bool
    province: str


class RecommendationHistoryResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    user_rank: int
    science_type: str
    target_year: int
    total_recommendations: int
    rush_count: int
    stable_count: int
    safe_count: int
    user_rating: Optional[int]
    user_feedback: Optional[str]
    created_at: str
    config_name: str


@router.post("/configs", response_model=RecommendationConfigResponse, summary="创建推荐配置")
async def create_recommendation_config(
    config_data: RecommendationConfigCreate,
    current_user: User = Depends(require_permission(Permission.RECOMMENDATION_CREATE))
):
    """
    创建新的推荐配置
    
    - **name**: 配置名称
    - **权重配置**: 各项权重之和应为1.0
    - **比例配置**: 冲稳保比例之和应为1.0
    """
    # 验证权重配置
    total_weight = (
        config_data.rank_match_weight + 
        config_data.school_tier_weight + 
        config_data.major_popularity_weight + 
        config_data.location_preference_weight
    )
    if abs(total_weight - 1.0) > 0.01:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="权重配置总和必须为1.0"
        )
    
    # 验证比例配置
    total_ratio = config_data.rush_ratio + config_data.stable_ratio + config_data.safe_ratio
    if abs(total_ratio - 1.0) > 0.01:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="推荐比例总和必须为1.0"
        )
    
    # 创建配置
    config = await RecommendationConfig.create(
        user=current_user,
        **config_data.dict()
    )
    
    return RecommendationConfigResponse.model_validate(config)


@router.get("/configs", response_model=List[RecommendationConfigResponse], summary="获取推荐配置列表")
async def get_recommendation_configs(
    current_user: User = Depends(require_permission(Permission.RECOMMENDATION_VIEW))
):
    """获取当前用户的所有推荐配置"""
    configs = await RecommendationConfig.filter(
        user=current_user, 
        is_active=True
    ).order_by("-created_at").all()
    
    return [RecommendationConfigResponse.model_validate(config) for config in configs]


@router.get("/configs/{config_id}", response_model=RecommendationConfigResponse, summary="获取推荐配置详情")
async def get_recommendation_config(
    config_id: int,
    current_user: User = Depends(require_permission(Permission.RECOMMENDATION_VIEW))
):
    """获取指定推荐配置的详情"""
    config = await RecommendationConfig.get_or_none(id=config_id, user=current_user)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐配置不存在"
        )
    
    return RecommendationConfigResponse.model_validate(config)


@router.put("/configs/{config_id}", response_model=RecommendationConfigResponse, summary="更新推荐配置")
async def update_recommendation_config(
    config_id: int,
    config_data: RecommendationConfigCreate,
    current_user: User = Depends(require_permission(Permission.RECOMMENDATION_MANAGE))
):
    """更新推荐配置"""
    config = await RecommendationConfig.get_or_none(id=config_id, user=current_user)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐配置不存在"
        )
    
    # 验证权重和比例配置
    total_weight = (
        config_data.rank_match_weight + 
        config_data.school_tier_weight + 
        config_data.major_popularity_weight + 
        config_data.location_preference_weight
    )
    if abs(total_weight - 1.0) > 0.01:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="权重配置总和必须为1.0"
        )
    
    total_ratio = config_data.rush_ratio + config_data.stable_ratio + config_data.safe_ratio
    if abs(total_ratio - 1.0) > 0.01:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="推荐比例总和必须为1.0"
        )
    
    # 更新配置
    for field, value in config_data.dict().items():
        setattr(config, field, value)
    
    await config.save()
    
    return RecommendationConfigResponse.model_validate(config)


@router.delete("/configs/{config_id}", summary="删除推荐配置")
async def delete_recommendation_config(
    config_id: int,
    current_user: User = Depends(require_permission(Permission.RECOMMENDATION_MANAGE))
):
    """删除推荐配置"""
    config = await RecommendationConfig.get_or_none(id=config_id, user=current_user)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐配置不存在"
        )

    if config.is_default:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除默认配置"
        )

    await config.delete()

    return {"message": "推荐配置删除成功"}


@router.post("/generate", summary="生成专业推荐")
async def generate_recommendations(
    request: RecommendationRequest,
    current_user: User = Depends(require_permission(Permission.RECOMMENDATION_CREATE))
):
    """
    生成专业推荐

    - **user_rank**: 用户位次
    - **science_type**: 科类（physics/history）
    - **config_id**: 配置ID（可选，不提供则使用默认配置）
    - **target_year**: 目标年份
    - **strategy_name**: 推荐策略
    """
    # 获取推荐配置
    if request.config_id:
        config = await RecommendationConfig.get_or_none(
            id=request.config_id,
            user=current_user,
            is_active=True
        )
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="推荐配置不存在"
            )
    else:
        # 使用默认配置或创建一个
        config = await RecommendationConfig.get_or_none(
            user=current_user,
            is_default=True,
            is_active=True
        )
        if not config:
            # 创建默认配置
            config = await RecommendationConfig.create(
                user=current_user,
                name="默认配置",
                description="系统自动创建的默认配置",
                is_default=True
            )

    try:
        # 生成推荐
        history = await recommendation_engine.generate_recommendations(
            user=current_user,
            user_rank=request.user_rank,
            science_type=request.science_type,
            config=config,
            target_year=request.target_year,
            strategy_name=request.strategy_name
        )

        return {
            "message": "推荐生成成功",
            "history_id": history.id,
            "total_recommendations": history.total_recommendations,
            "rush_count": history.rush_count,
            "stable_count": history.stable_count,
            "safe_count": history.safe_count
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"推荐生成失败: {str(e)}"
        )


@router.get("/history", response_model=List[RecommendationHistoryResponse], summary="获取推荐历史")
async def get_recommendation_history(
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(require_permission(Permission.RECOMMENDATION_VIEW))
):
    """获取当前用户的推荐历史"""
    histories = await RecommendationHistory.filter(
        user=current_user
    ).select_related("config").order_by("-created_at").offset(offset).limit(limit).all()

    result = []
    for history in histories:
        result.append(RecommendationHistoryResponse(
            id=history.id,
            user_rank=history.user_rank,
            science_type=history.science_type.value,
            target_year=history.target_year,
            total_recommendations=history.total_recommendations,
            rush_count=history.rush_count,
            stable_count=history.stable_count,
            safe_count=history.safe_count,
            user_rating=history.user_rating,
            user_feedback=history.user_feedback,
            created_at=history.created_at.isoformat(),
            config_name=history.config.name
        ))

    return result


@router.get("/history/{history_id}/results", response_model=List[RecommendationResultResponse], summary="获取推荐结果详情")
async def get_recommendation_results(
    history_id: int,
    recommendation_type: Optional[str] = Query(None, description="推荐类型筛选"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(require_permission(Permission.RECOMMENDATION_VIEW))
):
    """获取推荐结果详情"""
    # 验证推荐历史是否属于当前用户
    history = await RecommendationHistory.get_or_none(id=history_id, user=current_user)
    if not history:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐历史不存在"
        )

    # 构建查询
    query = RecommendationResult.filter(history=history).select_related(
        "university", "major", "admission_record"
    )

    if recommendation_type:
        query = query.filter(recommendation_type=recommendation_type)

    results = await query.order_by("overall_rank").offset(offset).limit(limit).all()

    response_data = []
    for result in results:
        response_data.append(RecommendationResultResponse(
            id=result.id,
            university_name=result.university.name,
            university_code=result.university.code,
            major_name=result.major.name,
            recommendation_type=result.recommendation_type,
            total_score=result.total_score,
            rank_match_score=result.rank_match_score,
            school_tier_score=result.school_tier_score,
            major_popularity_score=result.major_popularity_score,
            location_preference_score=result.location_preference_score,
            rank_in_type=result.rank_in_type,
            overall_rank=result.overall_rank,
            min_score=result.admission_record.min_score,
            max_score=result.admission_record.max_score,
            min_rank=result.admission_record.min_rank,
            max_rank=result.admission_record.max_rank,
            plan_count=result.admission_record.plan_count,
            actual_count=result.admission_record.actual_count,
            is_985=result.university.is_985,
            is_211=result.university.is_211,
            province=result.university.province.value
        ))

    return response_data
