"""
用户管理相关API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, EmailStr, ConfigDict

from ..core.security import get_current_active_user, require_permission
from ..models.user import User, UserRole, Permission

router = APIRouter()


# 请求模型
class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    role: UserRole = UserRole.USER


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None


# 响应模型
class UserResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    username: str
    email: str
    full_name: Optional[str]
    role: UserRole
    is_active: bool
    is_verified: bool
    created_at: str
    last_login: Optional[str]


@router.get("/", response_model=List[UserResponse], summary="获取用户列表")
async def get_users(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    role: Optional[UserRole] = Query(None, description="角色筛选"),
    is_active: Optional[bool] = Query(None, description="状态筛选"),
    current_user: User = Depends(require_permission(Permission.USER_VIEW))
):
    """获取用户列表"""
    query = User.all()
    
    if role:
        query = query.filter(role=role)
    
    if is_active is not None:
        query = query.filter(is_active=is_active)
    
    users = await query.offset(skip).limit(limit).order_by("-created_at").all()
    
    return [UserResponse.model_validate(user) for user in users]


@router.post("/", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(require_permission(Permission.USER_CREATE))
):
    """创建新用户"""
    # 检查用户名是否已存在
    existing_user = await User.get_or_none(username=user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    existing_email = await User.get_or_none(email=user_data.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )
    
    # 创建用户
    user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        role=user_data.role
    )
    user.set_password(user_data.password)
    await user.save()
    
    return UserResponse.model_validate(user)


@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: int,
    current_user: User = Depends(require_permission(Permission.USER_VIEW))
):
    """获取指定用户的详情"""
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return UserResponse.model_validate(user)


@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_user: User = Depends(require_permission(Permission.USER_EDIT))
):
    """更新用户信息"""
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查邮箱是否被其他用户使用
    if user_data.email and user_data.email != user.email:
        existing_email = await User.get_or_none(email=user_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被其他用户使用"
            )
    
    # 更新用户信息
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)
    
    await user.save()
    
    return UserResponse.model_validate(user)


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    current_user: User = Depends(require_permission(Permission.USER_DELETE))
):
    """删除用户"""
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 不能删除自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己"
        )
    
    # 不能删除超级管理员（除非自己也是超级管理员）
    if user.role == UserRole.SUPER_ADMIN and current_user.role != UserRole.SUPER_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，无法删除超级管理员"
        )
    
    await user.delete()
    
    return {"message": "用户删除成功"}


@router.post("/{user_id}/reset-password", summary="重置用户密码")
async def reset_user_password(
    user_id: int,
    new_password: str,
    current_user: User = Depends(require_permission(Permission.USER_EDIT))
):
    """重置用户密码"""
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    user.set_password(new_password)
    await user.save(update_fields=["password_hash"])
    
    return {"message": "密码重置成功"}


@router.post("/{user_id}/toggle-status", summary="切换用户状态")
async def toggle_user_status(
    user_id: int,
    current_user: User = Depends(require_permission(Permission.USER_EDIT))
):
    """切换用户激活状态"""
    user = await User.get_or_none(id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 不能禁用自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能禁用自己"
        )
    
    user.is_active = not user.is_active
    await user.save(update_fields=["is_active"])
    
    status_text = "激活" if user.is_active else "禁用"
    return {"message": f"用户{status_text}成功"}


@router.get("/roles/list", summary="获取角色列表")
async def get_roles(
    current_user: User = Depends(require_permission(Permission.USER_VIEW))
):
    """获取所有可用角色"""
    return {
        "roles": [
            {"value": role.value, "label": role.value}
            for role in UserRole
        ]
    }


@router.get("/permissions/list", summary="获取权限列表")
async def get_permissions(
    current_user: User = Depends(require_permission(Permission.USER_VIEW))
):
    """获取所有可用权限"""
    return {
        "permissions": [
            {"value": perm.value, "label": perm.value}
            for perm in Permission
        ]
    }
