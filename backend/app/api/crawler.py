"""
爬虫管理相关API
"""
import asyncio
import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from pydantic import BaseModel, ConfigDict

from ..core.security import get_current_active_user, require_permission
from ..models.user import User, Permission
from ..models.university import ScienceType
from ..models.system import DataUpdateStatus, CrawlLog, CrawlStatus, LogLevel
from ..services.crawler_service import start_crawling_task

router = APIRouter()


# 请求模型
class CrawlTaskCreate(BaseModel):
    task_name: str
    years: List[str] = ["2022", "2023", "2024"]
    science_types: List[ScienceType] = [ScienceType.PHYSICS, ScienceType.HISTORY]
    description: Optional[str] = None


class CrawlTaskControl(BaseModel):
    action: str  # start, pause, resume, cancel


# 响应模型
class CrawlTaskResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    data_type: str
    status: str
    total_records: int
    processed_records: int
    success_records: int
    failed_records: int
    progress_percentage: float
    started_at: Optional[str]
    completed_at: Optional[str]
    estimated_completion: Optional[str]
    error_message: Optional[str]
    created_at: str


class CrawlLogResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    task_id: str
    task_name: str
    level: str
    message: str
    current_step: Optional[str]
    progress_percentage: Optional[float]
    created_at: str


@router.post("/tasks", summary="创建爬取任务")
async def create_crawl_task(
    task_data: CrawlTaskCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(require_permission(Permission.CRAWLER_MANAGE))
):
    """
    创建新的爬取任务
    
    - **task_name**: 任务名称
    - **years**: 要爬取的年份列表
    - **science_types**: 要爬取的科类列表
    - **description**: 任务描述
    """
    # 检查是否有正在运行的任务
    running_task = await DataUpdateStatus.filter(
        status__in=[CrawlStatus.RUNNING, CrawlStatus.PENDING]
    ).first()
    
    if running_task:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="已有任务正在运行，请等待完成后再创建新任务"
        )
    
    # 生成任务ID
    task_id = f"crawl_{uuid.uuid4().hex[:8]}"
    
    # 在后台启动爬取任务
    background_tasks.add_task(
        start_crawling_task,
        task_id,
        task_data.years,
        task_data.science_types
    )
    
    return {
        "message": "爬取任务创建成功",
        "task_id": task_id,
        "task_name": task_data.task_name
    }


@router.get("/tasks", response_model=List[CrawlTaskResponse], summary="获取爬取任务列表")
async def get_crawl_tasks(
    limit: int = 20,
    offset: int = 0,
    status_filter: Optional[CrawlStatus] = None,
    current_user: User = Depends(require_permission(Permission.CRAWLER_VIEW))
):
    """获取爬取任务列表"""
    query = DataUpdateStatus.all()
    
    if status_filter:
        query = query.filter(status=status_filter)
    
    tasks = await query.order_by("-created_at").offset(offset).limit(limit).all()
    
    result = []
    for task in tasks:
        result.append(CrawlTaskResponse(
            id=task.id,
            data_type=task.data_type,
            status=task.status.value,
            total_records=task.total_records,
            processed_records=task.processed_records,
            success_records=task.success_records,
            failed_records=task.failed_records,
            progress_percentage=task.get_progress_percentage(),
            started_at=task.started_at.isoformat() if task.started_at else None,
            completed_at=task.completed_at.isoformat() if task.completed_at else None,
            estimated_completion=task.estimated_completion.isoformat() if task.estimated_completion else None,
            error_message=task.error_message,
            created_at=task.created_at.isoformat()
        ))
    
    return result


@router.get("/tasks/{task_id}", response_model=CrawlTaskResponse, summary="获取爬取任务详情")
async def get_crawl_task(
    task_id: int,
    current_user: User = Depends(require_permission(Permission.CRAWLER_VIEW))
):
    """获取指定爬取任务的详情"""
    task = await DataUpdateStatus.get_or_none(id=task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="爬取任务不存在"
        )
    
    return CrawlTaskResponse(
        id=task.id,
        data_type=task.data_type,
        status=task.status.value,
        total_records=task.total_records,
        processed_records=task.processed_records,
        success_records=task.success_records,
        failed_records=task.failed_records,
        progress_percentage=task.get_progress_percentage(),
        started_at=task.started_at.isoformat() if task.started_at else None,
        completed_at=task.completed_at.isoformat() if task.completed_at else None,
        estimated_completion=task.estimated_completion.isoformat() if task.estimated_completion else None,
        error_message=task.error_message,
        created_at=task.created_at.isoformat()
    )


@router.post("/tasks/{task_id}/control", summary="控制爬取任务")
async def control_crawl_task(
    task_id: int,
    control_data: CrawlTaskControl,
    current_user: User = Depends(require_permission(Permission.CRAWLER_MANAGE))
):
    """
    控制爬取任务
    
    - **action**: 操作类型
      - start: 开始任务
      - pause: 暂停任务
      - resume: 恢复任务
      - cancel: 取消任务
    """
    task = await DataUpdateStatus.get_or_none(id=task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="爬取任务不存在"
        )
    
    action = control_data.action.lower()
    
    if action == "pause":
        if task.status != CrawlStatus.RUNNING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能暂停正在运行的任务"
            )
        task.status = CrawlStatus.PAUSED
        await task.save()
        
    elif action == "resume":
        if task.status != CrawlStatus.PAUSED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能恢复已暂停的任务"
            )
        task.status = CrawlStatus.RUNNING
        await task.save()
        
    elif action == "cancel":
        if task.status not in [CrawlStatus.RUNNING, CrawlStatus.PAUSED, CrawlStatus.PENDING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能取消运行中、暂停或待执行的任务"
            )
        task.status = CrawlStatus.CANCELLED
        await task.save()
        
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的操作类型"
        )
    
    return {"message": f"任务{action}成功"}


@router.delete("/tasks/{task_id}", summary="删除爬取任务")
async def delete_crawl_task(
    task_id: int,
    current_user: User = Depends(require_permission(Permission.CRAWLER_MANAGE))
):
    """删除爬取任务"""
    task = await DataUpdateStatus.get_or_none(id=task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="爬取任务不存在"
        )
    
    if task.status in [CrawlStatus.RUNNING, CrawlStatus.PENDING]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除正在运行或待执行的任务"
        )
    
    # 删除相关日志
    await CrawlLog.filter(task_id__startswith=f"crawl_{task_id}").delete()
    
    # 删除任务
    await task.delete()
    
    return {"message": "任务删除成功"}


@router.get("/tasks/{task_id}/logs", response_model=List[CrawlLogResponse], summary="获取任务日志")
async def get_task_logs(
    task_id: int,
    limit: int = 100,
    offset: int = 0,
    level: Optional[LogLevel] = None,
    current_user: User = Depends(require_permission(Permission.CRAWLER_VIEW))
):
    """获取指定任务的日志"""
    # 验证任务是否存在
    task = await DataUpdateStatus.get_or_none(id=task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="爬取任务不存在"
        )
    
    # 构建查询
    query = CrawlLog.filter(task_id__startswith=f"crawl_{task_id}")
    
    if level:
        query = query.filter(level=level)
    
    logs = await query.order_by("-created_at").offset(offset).limit(limit).all()
    
    return [
        CrawlLogResponse(
            id=log.id,
            task_id=log.task_id,
            task_name=log.task_name,
            level=log.level.value,
            message=log.message,
            current_step=log.current_step,
            progress_percentage=log.progress_percentage,
            created_at=log.created_at.isoformat()
        )
        for log in logs
    ]


@router.get("/status", summary="获取爬虫系统状态")
async def get_crawler_status(
    current_user: User = Depends(require_permission(Permission.CRAWLER_VIEW))
):
    """获取爬虫系统状态"""
    # 统计任务状态
    total_tasks = await DataUpdateStatus.all().count()
    running_tasks = await DataUpdateStatus.filter(status=CrawlStatus.RUNNING).count()
    completed_tasks = await DataUpdateStatus.filter(status=CrawlStatus.COMPLETED).count()
    failed_tasks = await DataUpdateStatus.filter(status=CrawlStatus.FAILED).count()
    
    # 获取最近的任务
    recent_task = await DataUpdateStatus.all().order_by("-created_at").first()
    
    return {
        "system_status": "running" if running_tasks > 0 else "idle",
        "statistics": {
            "total_tasks": total_tasks,
            "running_tasks": running_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks
        },
        "recent_task": {
            "id": recent_task.id if recent_task else None,
            "status": recent_task.status.value if recent_task else None,
            "progress": recent_task.get_progress_percentage() if recent_task else 0,
            "created_at": recent_task.created_at.isoformat() if recent_task else None
        } if recent_task else None
    }
