"""
认证相关API
"""
from datetime import datetime, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr, ConfigDict

from ..core.security import (
    security_manager, 
    authenticate_user, 
    get_current_user, 
    get_current_active_user
)
from ..models.user import User, UserRole

router = APIRouter()


# 请求模型
class UserRegister(BaseModel):
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None


class UserLogin(BaseModel):
    username: str
    password: str


class PasswordChange(BaseModel):
    old_password: str
    new_password: str


class PasswordReset(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str


# 响应模型
class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class UserProfile(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    username: str
    email: str
    full_name: Optional[str]
    role: UserRole
    is_active: bool
    is_verified: bool
    last_login: Optional[datetime]
    created_at: datetime


@router.post("/register", response_model=UserProfile, summary="用户注册")
async def register(user_data: UserRegister):
    """
    用户注册

    - **username**: 用户名（唯一）
    - **email**: 邮箱地址（唯一）
    - **password**: 密码
    - **full_name**: 真实姓名（可选）
    """
    # 检查用户名是否已存在
    existing_user = await User.get_or_none(username=user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    existing_email = await User.get_or_none(email=user_data.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )

    # 创建新用户
    user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        role=UserRole.USER  # 默认为普通用户
    )
    user.set_password(user_data.password)
    await user.save()

    return UserProfile.model_validate(user)


@router.post("/login", response_model=Token, summary="用户登录")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    用户登录
    
    - **username**: 用户名
    - **password**: 密码
    """
    user = await authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    await user.save(update_fields=["last_login"])
    
    # 创建访问令牌
    access_token = security_manager.create_access_token(
        data={"sub": str(user.id), "username": user.username}
    )
    
    # 创建刷新令牌
    refresh_token = security_manager.create_refresh_token(
        data={"sub": str(user.id), "username": user.username}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=security_manager.access_token_expire_minutes * 60
    )


@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(refresh_token: str):
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    payload = security_manager.verify_token(refresh_token, "refresh")
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )
    
    user_id = payload.get("sub")
    user = await User.get_or_none(id=int(user_id))
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用"
        )
    
    # 创建新的访问令牌
    access_token = security_manager.create_access_token(
        data={"sub": str(user.id), "username": user.username}
    )
    
    # 创建新的刷新令牌
    new_refresh_token = security_manager.create_refresh_token(
        data={"sub": str(user.id), "username": user.username}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=new_refresh_token,
        expires_in=security_manager.access_token_expire_minutes * 60
    )


@router.get("/me", response_model=UserProfile, summary="获取当前用户信息")
async def get_current_user_profile(current_user: User = Depends(get_current_active_user)):
    """获取当前登录用户的详细信息"""
    return UserProfile.model_validate(current_user)


@router.put("/me", response_model=UserProfile, summary="更新用户信息")
async def update_current_user(
    full_name: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """
    更新当前用户信息
    
    - **full_name**: 真实姓名
    """
    if full_name is not None:
        current_user.full_name = full_name
        await current_user.save(update_fields=["full_name"])
    
    return UserProfile.model_validate(current_user)


@router.post("/change-password", summary="修改密码")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user)
):
    """
    修改密码
    
    - **old_password**: 旧密码
    - **new_password**: 新密码
    """
    if not current_user.verify_password(password_data.old_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="旧密码错误"
        )
    
    current_user.set_password(password_data.new_password)
    await current_user.save(update_fields=["password_hash"])
    
    return {"message": "密码修改成功"}


@router.post("/logout", summary="用户登出")
async def logout(current_user: User = Depends(get_current_user)):
    """
    用户登出
    注意：由于使用JWT，实际的登出需要在客户端删除令牌
    """
    return {"message": "登出成功"}


@router.get("/permissions", summary="获取当前用户权限")
async def get_current_user_permissions(current_user: User = Depends(get_current_active_user)):
    """获取当前用户的所有权限"""
    permissions = current_user.get_permissions()
    return {
        "permissions": [perm.value for perm in permissions],
        "role": current_user.role.value
    }
