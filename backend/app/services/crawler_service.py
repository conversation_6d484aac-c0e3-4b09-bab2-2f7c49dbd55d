"""
爬虫服务 - 集成现有的spider系统
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import httpx
from pathlib import Path

from ..core.config import settings
from ..models.university import University, Province, ScienceType
from ..models.major import Major, MajorCategory
from ..models.admission import AdmissionRecord
from ..models.system import CrawlLog, DataUpdateStatus, CrawlStatus, LogLevel

logger = logging.getLogger(__name__)


class CrawlerConfig:
    """爬虫配置类"""

    def __init__(self):
        self.base_url = settings.crawler_api_base_url
        self.api_secret = settings.crawler_api_secret
        self.max_concurrent = settings.crawler_max_concurrent
        self.request_delay = settings.crawler_request_delay
        self.max_retries = settings.crawler_max_retries
        self.request_timeout = settings.crawler_request_timeout

        # API端点配置
        self.endpoints = {
            "school_list": "https://applet.cqzk.com.cn/prod/search/front/findTableCollage",
            "province_list": "https://applet.cqzk.com.cn/prod/login/common/dictList",
            "batch_list": "https://applet.cqzk.com.cn/prod/login/common/dictList",
            "school_detail": "https://applet.cqzk.com.cn/prod/history/front/history/ptwlList",
            "major_detail": "https://applet.cqzk.com.cn/prod/history/front/history/ptwlMajorList"
        }

        # 请求头配置
        self.headers = {
            "content-type": "application/json",
            "secret": self.api_secret,
            "token": "{{token}}",  # 需要动态获取
            "x-realip": "127.0.0.1, ***********, cardNo:SYSTEM"
        }

        # 科类映射
        self.science_mapping = {
            ScienceType.PHYSICS: "5",  # 物理类
            ScienceType.HISTORY: "1"  # 历史类
        }

        # 批次映射
        self.batch_mapping = {
            "0": "本科提前批A段",
            "1": "本科提前批B段",
            "2": "本科批",
            "5": "高职专科批",
            "8": "高职专科批提前批"
        }


class GaokaoCrawler:
    """高考数据爬虫"""

    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.client = None
        self.current_task_id = None
        self.current_status = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.client = httpx.AsyncClient(
            timeout=self.config.request_timeout,
            limits=httpx.Limits(max_connections=self.config.max_concurrent)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.client:
            await self.client.aclose()

    async def log_message(self, level: LogLevel, message: str, details: Dict = None):
        """记录日志"""
        if self.current_task_id:
            await CrawlLog.create(
                task_id=self.current_task_id,
                task_name="高考数据爬取",
                level=level,
                message=message,
                details=details or {}
            )
        logger.log(getattr(logging, level.value), message)

    async def update_status(self, status: CrawlStatus, progress: float = None, error_msg: str = None):
        """更新任务状态"""
        if self.current_status:
            self.current_status.status = status
            if progress is not None:
                # 根据进度计算已处理记录数
                self.current_status.processed_records = int(
                    self.current_status.total_records * progress / 100
                )
            if error_msg:
                self.current_status.error_message = error_msg
            await self.current_status.save()

    async def make_request(self, endpoint: str, data: Dict) -> Optional[Dict]:
        """发送API请求"""
        url = self.config.endpoints.get(endpoint)
        if not url:
            raise ValueError(f"未知的端点: {endpoint}")

        for attempt in range(self.config.max_retries):
            try:
                response = await self.client.post(
                    url,
                    headers=self.config.headers,
                    json=data
                )
                response.raise_for_status()
                result = response.json()

                if result.get("success"):
                    return result.get("data", [])
                else:
                    await self.log_message(
                        LogLevel.WARNING,
                        f"API返回错误: {result.get('msg', '未知错误')}"
                    )
                    return None

            except Exception as e:
                await self.log_message(
                    LogLevel.WARNING,
                    f"请求失败 (尝试 {attempt + 1}/{self.config.max_retries}): {str(e)}"
                )
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(self.config.request_delay * (attempt + 1))
                else:
                    raise

        return None

    async def get_school_list(self) -> List[Dict]:
        """获取学校列表"""
        await self.log_message(LogLevel.INFO, "开始获取学校列表")

        # 获取所有省份的学校
        provinces = ",".join([p.value for p in Province])

        data = {
            "province": provinces,
            "type": "collageName",
            "value": "",
            "table": "PTWL"
        }

        schools = await self.make_request("school_list", data)
        if schools:
            await self.log_message(LogLevel.INFO, f"获取到 {len(schools)} 所学校")
            return schools

        return []

    async def get_major_details(self, school_code: str, year: str, science_type: ScienceType) -> List[Dict]:
        """获取专业详情数据"""
        science_code = self.config.science_mapping.get(science_type, "5")

        data = {
            "routeId": f"{datetime.now().strftime('%Y%m%d%H%M%S')}-SYSTEM",
            "year": year,
            "science": science_code,
            "province": "",
            "collage": school_code,
            "is211": "",
            "is985": "",
            "sfyldx": "",
            "majorName": "",
            "majorNameNoLike": "",
            "front_WC": 1,
            "batch": "",
            "minScoreLine": None,
            "sexRemark": ""
        }

        majors = await self.make_request("major_detail", data)
        if majors:
            await self.log_message(
                LogLevel.DEBUG,
                f"学校 {school_code} {year}年{science_type.value}类获取到 {len(majors)} 个专业"
            )
            return majors

        return []

    async def process_school_data(self, school_data: Dict) -> Optional[University]:
        """处理学校数据"""
        try:
            # 检查学校是否已存在
            university = await University.get_or_none(code=school_data["collage"])

            if not university:
                # 创建新学校
                university = await University.create(
                    code=school_data["collage"],
                    name=school_data["collageName"],
                    province=Province.BEIJING,  # 默认值，后续需要从详细信息中获取
                    is_985=False,  # 默认值，后续需要从详细信息中获取
                    is_211=False,  # 默认值，后续需要从详细信息中获取
                    is_double_first_class=False
                )
                await self.log_message(
                    LogLevel.INFO,
                    f"创建新学校: {university.name} ({university.code})"
                )

            return university

        except Exception as e:
            await self.log_message(
                LogLevel.ERROR,
                f"处理学校数据失败: {str(e)}",
                {"school_data": school_data}
            )
            return None

    async def process_major_data(self, major_data: Dict, university: University) -> Optional[AdmissionRecord]:
        """处理专业数据"""
        try:
            # 获取或创建专业
            major = await Major.get_or_none(name=major_data["majorName"])
            if not major:
                major = await Major.create(
                    name=major_data["majorName"],
                    code=major_data.get("majorCode"),
                    category=self._guess_major_category(major_data["majorName"])
                )

            # 创建录取记录
            admission_record = await AdmissionRecord.create(
                university=university,
                major=major,
                year=int(major_data["year"]),
                province="重庆",  # 默认重庆，因为这是重庆的API
                batch=self._map_batch(major_data.get("batch", "")),
                science_type=self._map_science_type(major_data.get("science", "5")),
                plan_count=self._safe_int(major_data.get("majorPlan")),
                actual_count=self._safe_int(major_data.get("majorReal")),
                max_score=self._safe_int(major_data.get("majorMaxScore")),
                min_score=self._safe_int(major_data.get("majorMinScore")),
                avg_score=self._safe_float(major_data.get("majorAvgScore")),
                max_rank=self._safe_int(major_data.get("majorMaxScoreRank")),
                min_rank=self._safe_int(major_data.get("majorMinScoreRank")),
                subject_requirements=major_data.get("majorRequirementName", "")
            )

            return admission_record

        except Exception as e:
            await self.log_message(
                LogLevel.ERROR,
                f"处理专业数据失败: {str(e)}",
                {"major_data": major_data}
            )
            return None

    def _guess_major_category(self, major_name: str) -> Optional[MajorCategory]:
        """根据专业名称猜测专业大类"""
        category_keywords = {
            MajorCategory.ENGINEERING: ["工程", "技术", "计算机", "软件", "电子", "机械", "建筑", "土木"],
            MajorCategory.ECONOMICS: ["经济", "金融", "财政", "贸易", "商务"],
            MajorCategory.MANAGEMENT: ["管理", "工商", "会计", "市场", "人力资源"],
            MajorCategory.MEDICINE: ["医学", "临床", "护理", "药学", "中医"],
            MajorCategory.SCIENCE: ["数学", "物理", "化学", "生物", "统计"],
            MajorCategory.LITERATURE: ["中文", "外语", "英语", "新闻", "传播"],
            MajorCategory.LAW: ["法学", "法律", "政治", "社会"],
            MajorCategory.EDUCATION: ["教育", "师范", "体育"],
            MajorCategory.ART: ["艺术", "美术", "音乐", "设计", "戏剧"]
        }

        for category, keywords in category_keywords.items():
            if any(keyword in major_name for keyword in keywords):
                return category

        return None

    def _map_batch(self, batch_code: str) -> str:
        """映射批次代码"""
        return self.config.batch_mapping.get(batch_code, "其他")

    def _map_science_type(self, science_code: str) -> ScienceType:
        """映射科类代码"""
        if science_code == "5":
            return ScienceType.PHYSICS
        elif science_code == "1":
            return ScienceType.HISTORY
        else:
            return ScienceType.PHYSICS  # 默认物理类

    def _safe_int(self, value: Any) -> Optional[int]:
        """安全转换为整数"""
        if value is None or value == "":
            return None
        try:
            return int(float(str(value)))
        except (ValueError, TypeError):
            return None

    def _safe_float(self, value: Any) -> Optional[float]:
        """安全转换为浮点数"""
        if value is None or value == "":
            return None
        try:
            return float(str(value))
        except (ValueError, TypeError):
            return None

    async def crawl_all_data(self, task_id: str, years: List[str] = None, science_types: List[ScienceType] = None):
        """爬取所有数据"""
        self.current_task_id = task_id

        if years is None:
            years = ["2022", "2023", "2024"]

        if science_types is None:
            science_types = [ScienceType.PHYSICS, ScienceType.HISTORY]

        # 创建数据更新状态记录
        self.current_status = await DataUpdateStatus.create(
            data_type="高考录取数据",
            status=CrawlStatus.RUNNING,
            started_at=datetime.utcnow()
        )

        try:
            await self.log_message(LogLevel.INFO, "开始爬取高考数据")
            await self.update_status(CrawlStatus.RUNNING, 0)

            # 获取学校列表
            schools = await self.get_school_list()
            if not schools:
                raise Exception("获取学校列表失败")

            total_tasks = len(schools) * len(years) * len(science_types)
            self.current_status.total_records = total_tasks
            await self.current_status.save()

            processed_count = 0
            success_count = 0

            # 遍历学校
            for school_data in schools:
                university = await self.process_school_data(school_data)
                if not university:
                    continue

                # 遍历年份和科类
                for year in years:
                    for science_type in science_types:
                        try:
                            majors = await self.get_major_details(
                                school_data["collage"], year, science_type
                            )

                            for major_data in majors:
                                admission_record = await self.process_major_data(major_data, university)
                                if admission_record:
                                    success_count += 1

                            processed_count += 1
                            progress = (processed_count / total_tasks) * 100
                            await self.update_status(CrawlStatus.RUNNING, progress)

                            # 请求间隔
                            await asyncio.sleep(self.config.request_delay)

                        except Exception as e:
                            await self.log_message(
                                LogLevel.ERROR,
                                f"处理学校 {university.name} {year}年{science_type.value}类数据失败: {str(e)}"
                            )

            # 完成爬取
            self.current_status.success_records = success_count
            self.current_status.completed_at = datetime.utcnow()
            await self.update_status(CrawlStatus.COMPLETED, 100)

            await self.log_message(
                LogLevel.INFO,
                f"爬取完成，成功处理 {success_count} 条记录"
            )

        except Exception as e:
            await self.log_message(LogLevel.ERROR, f"爬取失败: {str(e)}")
            await self.update_status(CrawlStatus.FAILED, error_msg=str(e))
            raise


# 全局爬虫配置和实例
crawler_config = CrawlerConfig()


async def start_crawling_task(task_id: str, years: List[str] = None, science_types: List[ScienceType] = None):
    """启动爬取任务"""
    async with GaokaoCrawler(crawler_config) as crawler:
        await crawler.crawl_all_data(task_id, years, science_types)
