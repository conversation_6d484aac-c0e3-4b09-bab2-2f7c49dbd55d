"""
专业推荐服务
基于多维度算法的智能推荐系统
"""
from typing import List, Dict, Any, Optional, Tuple
from abc import ABC, abstractmethod
import math
import logging
from datetime import datetime

from ..models.user import User
from ..models.university import University, ScienceType
from ..models.major import Major
from ..models.admission import AdmissionRecord
from ..models.recommendation import (
    RecommendationConfig, 
    RecommendationHistory, 
    RecommendationResult
)

logger = logging.getLogger(__name__)


class RecommendationStrategy(ABC):
    """推荐策略抽象基类"""
    
    @abstractmethod
    async def calculate_score(
        self, 
        user_rank: int, 
        admission_record: AdmissionRecord, 
        config: RecommendationConfig
    ) -> Dict[str, float]:
        """计算推荐分数"""
        pass


class StandardRecommendationStrategy(RecommendationStrategy):
    """标准推荐策略"""
    
    async def calculate_score(
        self, 
        user_rank: int, 
        admission_record: AdmissionRecord, 
        config: RecommendationConfig
    ) -> Dict[str, float]:
        """计算推荐分数"""
        
        # 1. 位次匹配度分数 (0-100)
        rank_match_score = self._calculate_rank_match_score(user_rank, admission_record)
        
        # 2. 学校层次分数 (0-100)
        school_tier_score = await self._calculate_school_tier_score(admission_record.university)
        
        # 3. 专业热度分数 (0-100)
        major_popularity_score = await self._calculate_major_popularity_score(admission_record.major)
        
        # 4. 地域偏好分数 (0-100)
        location_preference_score = self._calculate_location_preference_score(
            admission_record.university, config
        )
        
        # 5. 计算总分
        total_score = (
            rank_match_score * config.rank_match_weight +
            school_tier_score * config.school_tier_weight +
            major_popularity_score * config.major_popularity_weight +
            location_preference_score * config.location_preference_weight
        )
        
        return {
            "total_score": total_score,
            "rank_match_score": rank_match_score,
            "school_tier_score": school_tier_score,
            "major_popularity_score": major_popularity_score,
            "location_preference_score": location_preference_score,
        }
    
    def _calculate_rank_match_score(self, user_rank: int, admission_record: AdmissionRecord) -> float:
        """计算位次匹配度分数"""
        if not admission_record.min_rank or not admission_record.max_rank:
            return 0.0
        
        min_rank = admission_record.min_rank
        max_rank = admission_record.max_rank
        
        if user_rank < max_rank:
            # 用户位次高于最高位次，冲刺院校
            diff = max_rank - user_rank
            # 使用指数衰减函数，差距越大分数越低
            score = 100 * math.exp(-diff / 2000)
            return max(score, 20)  # 最低20分
        elif user_rank > min_rank:
            # 用户位次低于最低位次，保底院校
            diff = user_rank - min_rank
            score = 100 * math.exp(-diff / 3000)
            return max(score, 10)  # 最低10分
        else:
            # 用户位次在录取范围内，稳妥院校
            # 根据在范围内的位置给分
            range_size = min_rank - max_rank
            position = user_rank - max_rank
            relative_position = position / range_size if range_size > 0 else 0.5
            
            # 中间位置分数最高，两端稍低
            score = 100 - 10 * abs(relative_position - 0.5)
            return max(score, 80)
    
    async def _calculate_school_tier_score(self, university: University) -> float:
        """计算学校层次分数"""
        base_score = university.get_tier_score()
        
        # 根据排名调整分数
        if university.ranking_national:
            if university.ranking_national <= 10:
                base_score += 10
            elif university.ranking_national <= 50:
                base_score += 5
            elif university.ranking_national <= 100:
                base_score += 2
        
        return min(base_score, 100)
    
    async def _calculate_major_popularity_score(self, major: Major) -> float:
        """计算专业热度分数"""
        # 基础热度分数
        base_score = min(major.popularity_score, 100)
        
        # 根据就业率调整
        if major.employment_rate:
            if major.employment_rate >= 0.9:
                base_score += 10
            elif major.employment_rate >= 0.8:
                base_score += 5
            elif major.employment_rate < 0.6:
                base_score -= 10
        
        # 根据平均薪资调整
        if major.average_salary:
            if major.average_salary >= 15000:
                base_score += 10
            elif major.average_salary >= 10000:
                base_score += 5
            elif major.average_salary < 6000:
                base_score -= 5
        
        return max(min(base_score, 100), 0)
    
    def _calculate_location_preference_score(
        self, 
        university: University, 
        config: RecommendationConfig
    ) -> float:
        """计算地域偏好分数"""
        province = university.province.value
        
        # 检查是否在排除省份中
        if province in config.excluded_provinces:
            return 0.0
        
        # 检查是否在偏好省份中
        if config.preferred_provinces:
            if province in config.preferred_provinces:
                return 100.0
            else:
                return 30.0  # 不在偏好省份但也不排除
        
        # 没有设置偏好，默认分数
        return 60.0


class ConservativeRecommendationStrategy(RecommendationStrategy):
    """保守推荐策略"""
    
    async def calculate_score(
        self, 
        user_rank: int, 
        admission_record: AdmissionRecord, 
        config: RecommendationConfig
    ) -> Dict[str, float]:
        """保守策略：更重视位次匹配和学校层次"""
        
        # 调整权重，更重视位次匹配和学校层次
        adjusted_config = RecommendationConfig(
            rank_match_weight=0.5,
            school_tier_weight=0.4,
            major_popularity_weight=0.05,
            location_preference_weight=0.05,
        )
        
        standard_strategy = StandardRecommendationStrategy()
        return await standard_strategy.calculate_score(user_rank, admission_record, adjusted_config)


class RecommendationEngine:
    """推荐引擎"""
    
    def __init__(self):
        self.strategies = {
            "standard": StandardRecommendationStrategy(),
            "conservative": ConservativeRecommendationStrategy(),
        }
    
    async def generate_recommendations(
        self,
        user: User,
        user_rank: int,
        science_type: ScienceType,
        config: RecommendationConfig,
        target_year: int = 2024,
        strategy_name: str = "standard"
    ) -> RecommendationHistory:
        """生成推荐结果"""
        
        logger.info(f"开始为用户 {user.username} 生成推荐，位次: {user_rank}, 科类: {science_type}")
        
        # 获取推荐策略
        strategy = self.strategies.get(strategy_name, self.strategies["standard"])
        
        # 查询符合条件的录取记录
        admission_records = await self._get_candidate_records(
            user_rank, science_type, config, target_year
        )
        
        logger.info(f"找到 {len(admission_records)} 条候选录取记录")
        
        # 计算推荐分数
        scored_records = []
        for record in admission_records:
            try:
                scores = await strategy.calculate_score(user_rank, record, config)
                scored_records.append((record, scores))
            except Exception as e:
                logger.error(f"计算推荐分数失败: {e}")
                continue
        
        # 按总分排序
        scored_records.sort(key=lambda x: x[1]["total_score"], reverse=True)
        
        # 限制推荐数量
        max_recommendations = min(config.max_recommendations, len(scored_records))
        scored_records = scored_records[:max_recommendations]
        
        # 分类推荐结果
        categorized_results = self._categorize_recommendations(
            scored_records, user_rank, config
        )
        
        # 创建推荐历史记录
        history = await self._create_recommendation_history(
            user, config, user_rank, science_type, target_year, categorized_results
        )
        
        # 创建详细推荐结果
        await self._create_recommendation_results(history, categorized_results)
        
        logger.info(f"推荐生成完成，总计 {len(scored_records)} 条推荐")
        
        return history
    
    async def _get_candidate_records(
        self,
        user_rank: int,
        science_type: ScienceType,
        config: RecommendationConfig,
        target_year: int
    ) -> List[AdmissionRecord]:
        """获取候选录取记录"""
        
        # 基础查询条件
        query = AdmissionRecord.filter(
            science_type=science_type,
            year=target_year
        ).select_related("university", "major")
        
        # 位次范围过滤（扩大范围以包含冲刺和保底院校）
        rank_tolerance = config.min_score_tolerance * 3  # 扩大3倍范围
        query = query.filter(
            min_rank__gte=user_rank - rank_tolerance,
            max_rank__lte=user_rank + rank_tolerance
        )
        
        # 省份过滤
        if config.excluded_provinces:
            query = query.exclude(university__province__in=config.excluded_provinces)
        
        if config.preferred_provinces:
            query = query.filter(university__province__in=config.preferred_provinces)
        
        # 学校类型过滤
        if config.preferred_school_types:
            if "985" in config.preferred_school_types:
                query = query.filter(university__is_985=True)
            elif "211" in config.preferred_school_types:
                query = query.filter(university__is_211=True)
        
        # 专业大类过滤
        if config.excluded_major_categories:
            query = query.exclude(major__category__in=config.excluded_major_categories)
        
        if config.preferred_major_categories:
            query = query.filter(major__category__in=config.preferred_major_categories)
        
        return await query.all()
    
    def _categorize_recommendations(
        self,
        scored_records: List[Tuple[AdmissionRecord, Dict[str, float]]],
        user_rank: int,
        config: RecommendationConfig
    ) -> Dict[str, List[Tuple[AdmissionRecord, Dict[str, float]]]]:
        """分类推荐结果"""
        
        rush_list = []
        stable_list = []
        safe_list = []
        
        for record, scores in scored_records:
            if not record.min_rank or not record.max_rank:
                continue
            
            if user_rank < record.max_rank:
                # 冲刺院校
                rush_list.append((record, scores))
            elif user_rank > record.min_rank:
                # 保底院校
                safe_list.append((record, scores))
            else:
                # 稳妥院校
                stable_list.append((record, scores))
        
        # 按配置比例调整数量
        total_count = len(scored_records)
        rush_count = int(total_count * config.rush_ratio)
        stable_count = int(total_count * config.stable_ratio)
        safe_count = int(total_count * config.safe_ratio)
        
        return {
            "rush": rush_list[:rush_count],
            "stable": stable_list[:stable_count],
            "safe": safe_list[:safe_count],
        }
    
    async def _create_recommendation_history(
        self,
        user: User,
        config: RecommendationConfig,
        user_rank: int,
        science_type: ScienceType,
        target_year: int,
        categorized_results: Dict[str, List]
    ) -> RecommendationHistory:
        """创建推荐历史记录"""
        
        total_recommendations = sum(len(results) for results in categorized_results.values())
        
        history = await RecommendationHistory.create(
            user=user,
            config=config,
            user_rank=user_rank,
            science_type=science_type,
            target_year=target_year,
            total_recommendations=total_recommendations,
            rush_count=len(categorized_results["rush"]),
            stable_count=len(categorized_results["stable"]),
            safe_count=len(categorized_results["safe"]),
            recommendations={}  # 详细结果将在后续创建
        )
        
        return history
    
    async def _create_recommendation_results(
        self,
        history: RecommendationHistory,
        categorized_results: Dict[str, List[Tuple[AdmissionRecord, Dict[str, float]]]]
    ):
        """创建详细推荐结果"""
        
        overall_rank = 1
        
        for recommendation_type, results in categorized_results.items():
            rank_in_type = 1
            
            for record, scores in results:
                await RecommendationResult.create(
                    history=history,
                    university=record.university,
                    major=record.major,
                    admission_record=record,
                    recommendation_type=recommendation_type,
                    total_score=scores["total_score"],
                    rank_match_score=scores["rank_match_score"],
                    school_tier_score=scores["school_tier_score"],
                    major_popularity_score=scores["major_popularity_score"],
                    location_preference_score=scores["location_preference_score"],
                    rank_in_type=rank_in_type,
                    overall_rank=overall_rank
                )
                
                rank_in_type += 1
                overall_rank += 1


# 全局推荐引擎实例
recommendation_engine = RecommendationEngine()
