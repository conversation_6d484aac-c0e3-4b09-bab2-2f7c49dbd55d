"""
高考数据管理平台主应用
"""
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from .core.config import settings
from .core.database import init_db, init_redis, close_redis
from .api import api_router

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("正在启动高考数据管理平台...")

    try:
        # 初始化Redis连接
        await init_redis()

        # 创建默认管理员用户
        await create_default_admin()

        logger.info("高考数据管理平台启动完成")
    except Exception as e:
        logger.error(f"启动过程中出现错误: {e}")
        logger.info("继续启动，但某些功能可能不可用")

    yield

    # 关闭时执行
    logger.info("正在关闭高考数据管理平台...")
    try:
        await close_redis()
    except Exception as e:
        logger.error(f"关闭过程中出现错误: {e}")
    logger.info("高考数据管理平台已关闭")


async def create_default_admin():
    """创建默认管理员用户"""
    try:
        from .models.user import User, UserRole
        
        # 检查是否已存在管理员用户
        admin_exists = await User.filter(role=UserRole.SUPER_ADMIN).exists()
        if admin_exists:
            return
        
        # 创建默认管理员
        admin = User(
            username="admin",
            email="<EMAIL>",
            full_name="系统管理员",
            role=UserRole.SUPER_ADMIN,
            is_active=True,
            is_verified=True
        )
        admin.set_password("admin123")  # 默认密码，生产环境需要修改
        await admin.save()
        
        logger.info("默认管理员用户创建成功 - 用户名: admin, 密码: admin123")
        
    except Exception as e:
        logger.error(f"创建默认管理员用户失败: {e}")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于FastAPI + Vue3的高考数据收集、分析和推荐平台",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# 初始化数据库
init_db(app)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加受信任主机中间件
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.gaokao.com"]
    )

# 注册API路由
app.include_router(api_router, prefix=settings.api_prefix)


@app.get("/", summary="根路径")
async def root():
    """根路径，返回API信息"""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": "高考数据管理平台API",
        "docs_url": "/docs" if settings.debug else None,
        "api_prefix": settings.api_prefix
    }


@app.get("/health", summary="健康检查")
async def health_check():
    """健康检查接口"""
    try:
        from .core.database import get_redis
        
        # 检查数据库连接
        from tortoise import Tortoise
        db_status = "ok" if Tortoise._connections else "error"
        
        # 检查Redis连接
        redis_client = get_redis()
        redis_status = "ok"
        if redis_client:
            try:
                await redis_client.ping()
            except Exception:
                redis_status = "error"
        else:
            redis_status = "disconnected"
        
        return {
            "status": "ok",
            "database": db_status,
            "redis": redis_status,
            "version": settings.app_version
        }
    
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "error",
                "message": str(e),
                "version": settings.app_version
            }
        )


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": "服务器内部错误",
            "message": str(exc) if settings.debug else "请联系系统管理员"
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
