"""
录取记录相关模型
"""
from tortoise.models import Model
from tortoise import fields
from .university import University, Batch, ScienceType
from .major import Major


class AdmissionRecord(Model):
    """录取记录模型"""
    id = fields.IntField(pk=True)
    
    # 关联信息
    university = fields.ForeignKeyField("models.University", related_name="admission_records", description="大学")
    major = fields.ForeignKeyField("models.Major", related_name="admission_records", description="专业")
    
    # 基本信息
    year = fields.IntField(description="录取年份")
    province = fields.CharField(max_length=20, description="录取省份")
    batch = fields.CharEnumField(Batch, description="录取批次")
    science_type = fields.CharEnumField(ScienceType, description="科类")
    
    # 录取数据
    plan_count = fields.IntField(null=True, description="计划招生人数")
    actual_count = fields.IntField(null=True, description="实际录取人数")
    
    # 分数信息
    max_score = fields.IntField(null=True, description="最高分")
    min_score = fields.IntField(null=True, description="最低分")
    avg_score = fields.FloatField(null=True, description="平均分")
    
    # 位次信息
    max_rank = fields.IntField(null=True, description="最高分位次")
    min_rank = fields.IntField(null=True, description="最低分位次")
    avg_rank = fields.FloatField(null=True, description="平均位次")
    
    # 选科要求
    subject_requirements = fields.CharField(max_length=200, null=True, description="选科要求")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 扩展信息
    extra_info = fields.JSONField(default=dict, description="扩展信息")
    
    class Meta:
        table = "admission_records"
        table_description = "录取记录表"
        unique_together = [("university", "major", "year", "province", "science_type")]
        indexes = [
            ("year", "province", "science_type"),
            ("min_rank", "max_rank"),
            ("min_score", "max_score"),
            ("university_id", "year"),
            ("major_id", "year"),
        ]
    
    def get_score_range(self) -> tuple:
        """获取分数范围"""
        return (self.min_score, self.max_score)
    
    def get_rank_range(self) -> tuple:
        """获取位次范围"""
        return (self.min_rank, self.max_rank)
    
    def is_in_rank_range(self, user_rank: int, tolerance: int = 1000) -> bool:
        """判断用户位次是否在录取范围内（含容差）"""
        if not self.min_rank or not self.max_rank:
            return False
        
        return (self.max_rank - tolerance) <= user_rank <= (self.min_rank + tolerance)
    
    def calculate_match_score(self, user_rank: int) -> float:
        """计算位次匹配分数（0-100）"""
        if not self.min_rank or not self.max_rank:
            return 0.0
        
        if user_rank < self.max_rank:
            # 用户位次高于最高位次，冲刺院校
            diff = self.max_rank - user_rank
            return max(0, 100 - diff / 100)
        elif user_rank > self.min_rank:
            # 用户位次低于最低位次，保底院校
            diff = user_rank - self.min_rank
            return max(0, 100 - diff / 100)
        else:
            # 用户位次在录取范围内，稳妥院校
            return 100.0
    
    def __str__(self):
        return f"AdmissionRecord({self.university.name}-{self.major.name}-{self.year})"


class MajorAdmissionRecord(Model):
    """专业录取统计记录"""
    id = fields.IntField(pk=True)
    
    # 关联信息
    major = fields.ForeignKeyField("models.Major", related_name="major_admission_records", description="专业")
    
    # 统计信息
    year = fields.IntField(description="年份")
    total_plan = fields.IntField(default=0, description="总计划人数")
    total_actual = fields.IntField(default=0, description="总录取人数")
    university_count = fields.IntField(default=0, description="开设院校数")
    
    # 分数统计
    highest_score = fields.IntField(null=True, description="全国最高分")
    lowest_score = fields.IntField(null=True, description="全国最低分")
    avg_score = fields.FloatField(null=True, description="全国平均分")
    
    # 位次统计
    highest_rank = fields.IntField(null=True, description="全国最高位次")
    lowest_rank = fields.IntField(null=True, description="全国最低位次")
    avg_rank = fields.FloatField(null=True, description="全国平均位次")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        table = "major_admission_records"
        table_description = "专业录取统计表"
        unique_together = [("major", "year")]
        indexes = [
            ("year",),
            ("major_id", "year"),
        ]
    
    def __str__(self):
        return f"MajorAdmissionRecord({self.major.name}-{self.year})"
