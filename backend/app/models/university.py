"""
大学相关模型
"""
from tortoise.models import Model
from tortoise import fields
from enum import Enum


class Province(str, Enum):
    """省份枚举"""
    BEIJING = "北京"
    TIANJIN = "天津"
    HEBEI = "河北"
    SHANXI = "山西"
    INNER_MONGOLIA = "内蒙古"
    LIAONING = "辽宁"
    JILIN = "吉林"
    HEILONGJIANG = "黑龙江"
    SHANGHAI = "上海"
    JIANGSU = "江苏"
    ZHEJIANG = "浙江"
    ANHUI = "安徽"
    FUJIAN = "福建"
    JIANGXI = "江西"
    SHANDONG = "山东"
    HENAN = "河南"
    HUBEI = "湖北"
    HUNAN = "湖南"
    GUANGDONG = "广东"
    GUANGXI = "广西"
    HAINAN = "海南"
    CHONGQING = "重庆"
    SICHUAN = "四川"
    GUIZHOU = "贵州"
    YUNNAN = "云南"
    TIBET = "西藏"
    SHAANXI = "陕西"
    GANSU = "甘肃"
    QINGHAI = "青海"
    NINGXIA = "宁夏"
    XINJIANG = "新疆"


class Batch(str, Enum):
    """录取批次枚举"""
    EARLY_BATCH = "提前批"
    FIRST_BATCH = "本科一批"
    SECOND_BATCH = "本科二批"
    THIRD_BATCH = "本科三批"
    SPECIALIST = "专科批"
    OTHER = "其他"


class ScienceType(str, Enum):
    """科类枚举"""
    PHYSICS = "物理类"
    HISTORY = "历史类"
    LIBERAL_ARTS = "文科"
    SCIENCE = "理科"
    ART = "艺术类"
    SPORTS = "体育类"


class University(Model):
    """大学模型"""
    id = fields.IntField(pk=True)
    code = fields.CharField(max_length=20, unique=True, description="学校代码")
    name = fields.CharField(max_length=100, description="学校名称")
    short_name = fields.CharField(max_length=50, null=True, description="学校简称")
    english_name = fields.CharField(max_length=200, null=True, description="英文名称")
    
    # 基本信息
    province = fields.CharEnumField(Province, description="所在省份")
    city = fields.CharField(max_length=50, null=True, description="所在城市")
    address = fields.TextField(null=True, description="详细地址")
    
    # 学校性质
    is_985 = fields.BooleanField(default=False, description="是否985高校")
    is_211 = fields.BooleanField(default=False, description="是否211高校")
    is_double_first_class = fields.BooleanField(default=False, description="是否双一流高校")
    school_type = fields.CharField(max_length=50, null=True, description="学校类型")
    school_nature = fields.CharField(max_length=50, null=True, description="学校性质")
    
    # 联系信息
    website = fields.CharField(max_length=200, null=True, description="官方网站")
    admission_website = fields.CharField(max_length=200, null=True, description="招生网站")
    phone = fields.CharField(max_length=50, null=True, description="联系电话")
    email = fields.CharField(max_length=100, null=True, description="联系邮箱")
    
    # 统计信息
    total_majors = fields.IntField(default=0, description="专业总数")
    total_students = fields.IntField(null=True, description="在校学生数")
    faculty_count = fields.IntField(null=True, description="教职工数")
    
    # 排名信息
    ranking_national = fields.IntField(null=True, description="全国排名")
    ranking_provincial = fields.IntField(null=True, description="省内排名")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    last_crawled = fields.DatetimeField(null=True, description="最后爬取时间")
    
    # 扩展信息
    extra_info = fields.JSONField(default=dict, description="扩展信息")
    
    class Meta:
        table = "universities"
        table_description = "大学表"
        indexes = [
            ("province", "is_985", "is_211"),
            ("name",),
            ("code",),
        ]
    
    def get_tier_score(self) -> int:
        """获取学校层次分数"""
        if self.is_985:
            return 100
        elif self.is_211:
            return 80
        elif self.is_double_first_class:
            return 70
        else:
            return 50
    
    def __str__(self):
        return f"University(code={self.code}, name={self.name})"
