"""
推荐系统相关模型
"""
from tortoise.models import Model
from tortoise import fields
from typing import Dict, List, Any
from .user import User
from .university import ScienceType


class RecommendationConfig(Model):
    """推荐配置模型"""
    id = fields.IntField(pk=True)
    
    # 关联用户
    user = fields.ForeignKeyField("models.User", related_name="recommendation_configs", description="用户")
    
    # 配置基本信息
    name = fields.CharField(max_length=100, description="配置名称")
    description = fields.TextField(null=True, description="配置描述")
    
    # 权重配置
    rank_match_weight = fields.FloatField(default=0.4, description="位次匹配权重")
    school_tier_weight = fields.FloatField(default=0.3, description="学校层次权重")
    major_popularity_weight = fields.FloatField(default=0.2, description="专业热度权重")
    location_preference_weight = fields.FloatField(default=0.1, description="地域偏好权重")
    
    # 推荐比例配置
    rush_ratio = fields.FloatField(default=0.3, description="冲刺比例")
    stable_ratio = fields.FloatField(default=0.4, description="稳妥比例")
    safe_ratio = fields.FloatField(default=0.3, description="保底比例")
    
    # 筛选条件
    preferred_provinces = fields.JSONField(default=list, description="偏好省份")
    excluded_provinces = fields.JSONField(default=list, description="排除省份")
    preferred_school_types = fields.JSONField(default=list, description="偏好学校类型")
    preferred_major_categories = fields.JSONField(default=list, description="偏好专业大类")
    excluded_major_categories = fields.JSONField(default=list, description="排除专业大类")
    
    # 高级配置
    min_score_tolerance = fields.IntField(default=1000, description="最小分数容差")
    max_recommendations = fields.IntField(default=100, description="最大推荐数量")
    enable_985_priority = fields.BooleanField(default=True, description="985优先")
    enable_211_priority = fields.BooleanField(default=True, description="211优先")
    
    # 元数据
    is_default = fields.BooleanField(default=False, description="是否默认配置")
    is_active = fields.BooleanField(default=True, description="是否激活")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        table = "recommendation_configs"
        table_description = "推荐配置表"
        indexes = [
            ("user_id", "is_active"),
            ("is_default",),
        ]
    
    def validate_weights(self) -> bool:
        """验证权重配置是否合理"""
        total_weight = (
            self.rank_match_weight + 
            self.school_tier_weight + 
            self.major_popularity_weight + 
            self.location_preference_weight
        )
        return abs(total_weight - 1.0) < 0.01
    
    def validate_ratios(self) -> bool:
        """验证比例配置是否合理"""
        total_ratio = self.rush_ratio + self.stable_ratio + self.safe_ratio
        return abs(total_ratio - 1.0) < 0.01
    
    def get_weights_dict(self) -> Dict[str, float]:
        """获取权重字典"""
        return {
            "rank_match": self.rank_match_weight,
            "school_tier": self.school_tier_weight,
            "major_popularity": self.major_popularity_weight,
            "location_preference": self.location_preference_weight,
        }
    
    def get_ratios_dict(self) -> Dict[str, float]:
        """获取比例字典"""
        return {
            "rush": self.rush_ratio,
            "stable": self.stable_ratio,
            "safe": self.safe_ratio,
        }
    
    def __str__(self):
        return f"RecommendationConfig(name={self.name}, user={self.user.username})"


class RecommendationHistory(Model):
    """推荐历史模型"""
    id = fields.IntField(pk=True)
    
    # 关联信息
    user = fields.ForeignKeyField("models.User", related_name="recommendation_history", description="用户")
    config = fields.ForeignKeyField("models.RecommendationConfig", related_name="recommendation_history", description="使用的配置")
    
    # 推荐参数
    user_rank = fields.IntField(description="用户位次")
    science_type = fields.CharEnumField(ScienceType, description="科类")
    target_year = fields.IntField(description="目标年份")
    
    # 推荐结果统计
    total_recommendations = fields.IntField(description="推荐总数")
    rush_count = fields.IntField(description="冲刺院校数")
    stable_count = fields.IntField(description="稳妥院校数")
    safe_count = fields.IntField(description="保底院校数")
    
    # 推荐结果详情
    recommendations = fields.JSONField(description="推荐结果详情")
    
    # 用户反馈
    user_rating = fields.IntField(null=True, description="用户评分(1-5)")
    user_feedback = fields.TextField(null=True, description="用户反馈")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "recommendation_history"
        table_description = "推荐历史表"
        indexes = [
            ("user_id", "created_at"),
            ("user_rank", "science_type"),
            ("target_year",),
        ]
    
    def __str__(self):
        return f"RecommendationHistory(user={self.user.username}, rank={self.user_rank})"


class RecommendationResult(Model):
    """推荐结果详情模型"""
    id = fields.IntField(pk=True)
    
    # 关联信息
    history = fields.ForeignKeyField("models.RecommendationHistory", related_name="results", description="推荐历史")
    university = fields.ForeignKeyField("models.University", related_name="recommendation_results", description="大学")
    major = fields.ForeignKeyField("models.Major", related_name="recommendation_results", description="专业")
    admission_record = fields.ForeignKeyField("models.AdmissionRecord", related_name="recommendation_results", description="录取记录")
    
    # 推荐信息
    recommendation_type = fields.CharField(max_length=20, description="推荐类型(rush/stable/safe)")
    total_score = fields.FloatField(description="总分")
    rank_match_score = fields.FloatField(description="位次匹配分")
    school_tier_score = fields.FloatField(description="学校层次分")
    major_popularity_score = fields.FloatField(description="专业热度分")
    location_preference_score = fields.FloatField(description="地域偏好分")
    
    # 排序信息
    rank_in_type = fields.IntField(description="在同类型中的排名")
    overall_rank = fields.IntField(description="总体排名")
    
    # 用户操作
    is_favorited = fields.BooleanField(default=False, description="是否收藏")
    is_applied = fields.BooleanField(default=False, description="是否已报考")
    user_notes = fields.TextField(null=True, description="用户备注")
    
    class Meta:
        table = "recommendation_results"
        table_description = "推荐结果详情表"
        indexes = [
            ("history_id", "overall_rank"),
            ("recommendation_type", "rank_in_type"),
            ("total_score",),
        ]
    
    def __str__(self):
        return f"RecommendationResult({self.university.name}-{self.major.name})"
