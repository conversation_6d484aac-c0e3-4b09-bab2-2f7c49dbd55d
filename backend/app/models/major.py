"""
专业相关模型
"""
from tortoise.models import Model
from tortoise import fields
from enum import Enum


class MajorCategory(str, Enum):
    """专业大类枚举"""
    PHILOSOPHY = "哲学"
    ECONOMICS = "经济学"
    LAW = "法学"
    EDUCATION = "教育学"
    LITERATURE = "文学"
    HISTORY = "历史学"
    SCIENCE = "理学"
    ENGINEERING = "工学"
    AGRICULTURE = "农学"
    MEDICINE = "医学"
    MANAGEMENT = "管理学"
    ART = "艺术学"
    MILITARY = "军事学"


class Major(Model):
    """专业模型"""
    id = fields.IntField(pk=True)
    code = fields.CharField(max_length=20, null=True, description="专业代码")
    name = fields.CharField(max_length=100, description="专业名称")
    category = fields.CharEnumField(MajorCategory, null=True, description="专业大类")
    
    # 专业信息
    degree_type = fields.CharField(max_length=20, null=True, description="学位类型")
    duration = fields.IntField(default=4, description="学制年限")
    description = fields.TextField(null=True, description="专业描述")
    
    # 就业信息
    employment_rate = fields.FloatField(null=True, description="就业率")
    average_salary = fields.IntField(null=True, description="平均薪资")
    employment_prospects = fields.TextField(null=True, description="就业前景")
    
    # 热度统计
    popularity_score = fields.FloatField(default=0.0, description="专业热度分数")
    total_enrollments = fields.IntField(default=0, description="总录取人数")
    total_universities = fields.IntField(default=0, description="开设院校数")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 扩展信息
    extra_info = fields.JSONField(default=dict, description="扩展信息")
    
    class Meta:
        table = "majors"
        table_description = "专业表"
        indexes = [
            ("category",),
            ("name",),
            ("popularity_score",),
        ]
    
    def calculate_popularity_score(self) -> float:
        """计算专业热度分数"""
        # 基于录取人数和开设院校数计算热度
        if self.total_universities == 0:
            return 0.0
        
        # 热度 = (总录取人数 / 开设院校数) * 权重系数
        base_score = self.total_enrollments / self.total_universities
        
        # 根据专业大类调整权重
        category_weights = {
            MajorCategory.ENGINEERING: 1.2,
            MajorCategory.ECONOMICS: 1.1,
            MajorCategory.MANAGEMENT: 1.1,
            MajorCategory.MEDICINE: 1.0,
            MajorCategory.SCIENCE: 1.0,
            MajorCategory.LITERATURE: 0.9,
            MajorCategory.LAW: 0.9,
            MajorCategory.EDUCATION: 0.8,
            MajorCategory.ART: 0.8,
            MajorCategory.PHILOSOPHY: 0.7,
            MajorCategory.HISTORY: 0.7,
            MajorCategory.AGRICULTURE: 0.7,
            MajorCategory.MILITARY: 0.6,
        }
        
        weight = category_weights.get(self.category, 1.0)
        return base_score * weight
    
    async def update_popularity_score(self):
        """更新专业热度分数"""
        self.popularity_score = self.calculate_popularity_score()
        await self.save(update_fields=["popularity_score"])
    
    def __str__(self):
        return f"Major(name={self.name}, category={self.category})"
