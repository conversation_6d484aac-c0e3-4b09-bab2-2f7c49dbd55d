"""
用户管理相关模型
"""
from tortoise.models import Model
from tortoise import fields
from enum import Enum
from typing import List
import bcrypt


class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"      # 超级管理员
    DATA_ADMIN = "data_admin"        # 数据管理员
    ANALYST = "analyst"              # 分析师
    USER = "user"                    # 普通用户
    GUEST = "guest"                  # 访客


class Permission(str, Enum):
    """权限枚举"""
    # 爬虫管理权限
    CRAWLER_VIEW = "crawler:view"
    CRAWLER_MANAGE = "crawler:manage"
    CRAWLER_CONFIG = "crawler:config"
    
    # 数据管理权限
    DATA_VIEW = "data:view"
    DATA_EXPORT = "data:export"
    DATA_IMPORT = "data:import"
    DATA_DELETE = "data:delete"
    
    # 推荐系统权限
    RECOMMENDATION_VIEW = "recommendation:view"
    RECOMMENDATION_CREATE = "recommendation:create"
    RECOMMENDATION_MANAGE = "recommendation:manage"
    
    # 用户管理权限
    USER_VIEW = "user:view"
    USER_CREATE = "user:create"
    USER_EDIT = "user:edit"
    USER_DELETE = "user:delete"
    
    # 系统管理权限
    SYSTEM_CONFIG = "system:config"
    SYSTEM_BACKUP = "system:backup"
    SYSTEM_MONITOR = "system:monitor"


# 角色权限映射
ROLE_PERMISSIONS = {
    UserRole.SUPER_ADMIN: list(Permission),  # 超级管理员拥有所有权限
    UserRole.DATA_ADMIN: [
        Permission.CRAWLER_VIEW, Permission.CRAWLER_MANAGE, Permission.CRAWLER_CONFIG,
        Permission.DATA_VIEW, Permission.DATA_EXPORT, Permission.DATA_IMPORT, Permission.DATA_DELETE,
        Permission.RECOMMENDATION_VIEW,
        Permission.SYSTEM_MONITOR,
    ],
    UserRole.ANALYST: [
        Permission.CRAWLER_VIEW,
        Permission.DATA_VIEW, Permission.DATA_EXPORT,
        Permission.RECOMMENDATION_VIEW, Permission.RECOMMENDATION_CREATE, Permission.RECOMMENDATION_MANAGE,
    ],
    UserRole.USER: [
        Permission.DATA_VIEW,
        Permission.RECOMMENDATION_VIEW, Permission.RECOMMENDATION_CREATE,
    ],
    UserRole.GUEST: [
        Permission.DATA_VIEW,
        Permission.RECOMMENDATION_VIEW,
    ],
}


class User(Model):
    """用户模型"""
    id = fields.IntField(pk=True)
    username = fields.CharField(max_length=50, unique=True, description="用户名")
    email = fields.CharField(max_length=100, unique=True, description="邮箱")
    password_hash = fields.CharField(max_length=255, description="密码哈希")
    full_name = fields.CharField(max_length=100, null=True, description="真实姓名")
    role = fields.CharEnumField(UserRole, default=UserRole.USER, description="用户角色")
    is_active = fields.BooleanField(default=True, description="是否激活")
    is_verified = fields.BooleanField(default=False, description="是否验证邮箱")
    last_login = fields.DatetimeField(null=True, description="最后登录时间")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 用户偏好设置
    preferences = fields.JSONField(default=dict, description="用户偏好设置")
    
    class Meta:
        table = "users"
        table_description = "用户表"
    
    def set_password(self, password: str):
        """设置密码"""
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    def has_permission(self, permission: Permission) -> bool:
        """检查用户是否有指定权限"""
        if not self.is_active:
            return False
        
        role_permissions = ROLE_PERMISSIONS.get(self.role, [])
        return permission in role_permissions
    
    def get_permissions(self) -> List[Permission]:
        """获取用户所有权限"""
        if not self.is_active:
            return []
        
        return ROLE_PERMISSIONS.get(self.role, [])
    
    def __str__(self):
        return f"User(username={self.username}, role={self.role})"
