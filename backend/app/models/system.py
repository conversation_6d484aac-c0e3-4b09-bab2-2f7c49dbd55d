"""
系统管理相关模型
"""
from tortoise.models import Model
from tortoise import fields
from enum import Enum
from .user import User


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class CrawlStatus(str, Enum):
    """爬取状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CrawlLog(Model):
    """爬取日志模型"""
    id = fields.IntField(pk=True)
    
    # 任务信息
    task_id = fields.CharField(max_length=100, description="任务ID")
    task_name = fields.CharField(max_length=200, description="任务名称")
    
    # 日志信息
    level = fields.CharEnumField(LogLevel, description="日志级别")
    message = fields.TextField(description="日志消息")
    details = fields.JSONField(default=dict, description="详细信息")
    
    # 进度信息
    current_step = fields.CharField(max_length=100, null=True, description="当前步骤")
    progress_percentage = fields.FloatField(null=True, description="进度百分比")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "crawl_logs"
        table_description = "爬取日志表"
        indexes = [
            ("task_id", "created_at"),
            ("level", "created_at"),
            ("created_at",),
        ]
    
    def __str__(self):
        return f"CrawlLog(task_id={self.task_id}, level={self.level})"


class DataUpdateStatus(Model):
    """数据更新状态模型"""
    id = fields.IntField(pk=True)
    
    # 更新信息
    data_type = fields.CharField(max_length=50, description="数据类型")
    status = fields.CharEnumField(CrawlStatus, description="更新状态")
    
    # 统计信息
    total_records = fields.IntField(default=0, description="总记录数")
    processed_records = fields.IntField(default=0, description="已处理记录数")
    success_records = fields.IntField(default=0, description="成功记录数")
    failed_records = fields.IntField(default=0, description="失败记录数")
    
    # 时间信息
    started_at = fields.DatetimeField(null=True, description="开始时间")
    completed_at = fields.DatetimeField(null=True, description="完成时间")
    estimated_completion = fields.DatetimeField(null=True, description="预计完成时间")
    
    # 错误信息
    error_message = fields.TextField(null=True, description="错误消息")
    error_details = fields.JSONField(default=dict, description="错误详情")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        table = "data_update_status"
        table_description = "数据更新状态表"
        indexes = [
            ("data_type", "status"),
            ("status", "created_at"),
        ]
    
    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_records == 0:
            return 0.0
        return (self.processed_records / self.total_records) * 100
    
    def __str__(self):
        return f"DataUpdateStatus(data_type={self.data_type}, status={self.status})"


class SystemConfig(Model):
    """系统配置模型"""
    id = fields.IntField(pk=True)
    
    # 配置信息
    key = fields.CharField(max_length=100, unique=True, description="配置键")
    value = fields.TextField(description="配置值")
    data_type = fields.CharField(max_length=20, default="string", description="数据类型")
    
    # 配置元数据
    category = fields.CharField(max_length=50, description="配置分类")
    description = fields.TextField(null=True, description="配置描述")
    is_public = fields.BooleanField(default=False, description="是否公开")
    is_editable = fields.BooleanField(default=True, description="是否可编辑")
    
    # 操作信息
    created_by = fields.ForeignKeyField("models.User", related_name="created_configs", null=True, description="创建者")
    updated_by = fields.ForeignKeyField("models.User", related_name="updated_configs", null=True, description="更新者")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        table = "system_configs"
        table_description = "系统配置表"
        indexes = [
            ("category",),
            ("key",),
            ("is_public",),
        ]
    
    def get_typed_value(self):
        """获取类型化的值"""
        if self.data_type == "int":
            return int(self.value)
        elif self.data_type == "float":
            return float(self.value)
        elif self.data_type == "bool":
            return self.value.lower() in ("true", "1", "yes", "on")
        elif self.data_type == "json":
            import json
            return json.loads(self.value)
        else:
            return self.value
    
    def set_typed_value(self, value):
        """设置类型化的值"""
        if self.data_type == "json":
            import json
            self.value = json.dumps(value, ensure_ascii=False)
        else:
            self.value = str(value)
    
    def __str__(self):
        return f"SystemConfig(key={self.key}, category={self.category})"


class BackupRecord(Model):
    """备份记录模型"""
    id = fields.IntField(pk=True)
    
    # 备份信息
    backup_name = fields.CharField(max_length=200, description="备份名称")
    backup_type = fields.CharField(max_length=50, description="备份类型")
    file_path = fields.CharField(max_length=500, description="文件路径")
    file_size = fields.BigIntField(description="文件大小(字节)")
    
    # 备份内容
    included_tables = fields.JSONField(default=list, description="包含的表")
    record_count = fields.IntField(description="记录总数")
    
    # 操作信息
    created_by = fields.ForeignKeyField("models.User", related_name="backup_records", description="创建者")
    
    # 状态信息
    status = fields.CharField(max_length=20, default="completed", description="备份状态")
    error_message = fields.TextField(null=True, description="错误消息")
    
    # 时间信息
    started_at = fields.DatetimeField(description="开始时间")
    completed_at = fields.DatetimeField(null=True, description="完成时间")
    
    # 元数据
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "backup_records"
        table_description = "备份记录表"
        indexes = [
            ("backup_type", "created_at"),
            ("status", "created_at"),
            ("created_by_id", "created_at"),
        ]
    
    def get_file_size_human(self) -> str:
        """获取人类可读的文件大小"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def __str__(self):
        return f"BackupRecord(name={self.backup_name}, type={self.backup_type})"
