"""
测试未来年份支持

验证系统能够支持2025、2026等未来年份
"""

import sys
from pathlib import Path

# 添加spider模块到路径
sys.path.insert(0, str(Path(__file__).parent))

from spider.config import config
from spider.data_processor import DataProcessor


def test_future_years_config():
    """测试未来年份配置"""
    print("=== 测试未来年份配置 ===")
    
    # 测试用例
    test_cases = [
        {
            "name": "2025年单独",
            "years": ["2025"],
            "expected_columns": 13 + 7,  # 基础列13 + 2025年7列
            "expected_fields": ["25计划人数", "25录取人数", "25录取最高分", "25最高分位次", "25录取平均分", "25录取最低分", "25最低分位次"]
        },
        {
            "name": "2025-2026年",
            "years": ["2025", "2026"],
            "expected_columns": 13 + 14,  # 基础列13 + 两年14列
            "expected_fields": ["26计划人数", "26录取人数", "26录取最高分", "26最高分位次", "26录取平均分", "26录取最低分", "26最低分位次",
                              "25计划人数", "25录取人数", "25录取最高分", "25最高分位次", "25录取平均分", "25录取最低分", "25最低分位次"]
        },
        {
            "name": "2024-2026年",
            "years": ["2024", "2025", "2026"],
            "expected_columns": 13 + 21,  # 基础列13 + 三年21列
            "expected_fields": ["26计划人数", "25计划人数", "24计划人数"]  # 按年份倒序
        },
        {
            "name": "跨度年份2020-2025",
            "years": ["2020", "2021", "2022", "2023", "2024", "2025"],
            "expected_columns": 13 + 42,  # 基础列13 + 六年42列
            "expected_fields": ["25计划人数", "24计划人数", "23计划人数", "22计划人数", "21计划人数", "20计划人数"]
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {test_case['name']}")
        print(f"年份: {test_case['years']}")
        
        try:
            # 设置自定义年份
            config.set_custom_years(test_case['years'])
            
            # 检查Excel列配置
            actual_columns = len(config.output.excel_columns)
            expected_columns = test_case['expected_columns']
            
            print(f"Excel列数: {actual_columns} (期望: {expected_columns})")
            
            if actual_columns == expected_columns:
                print("✅ Excel列数正确")
            else:
                print("❌ Excel列数错误")
                return False
            
            # 检查年份字段
            print("年份字段检查:")
            for field in test_case['expected_fields']:
                if field in config.output.excel_columns:
                    print(f"  ✅ {field}")
                else:
                    print(f"  ❌ {field} 缺失")
                    return False
            
            # 显示完整的列配置（前几列和后几列）
            columns = config.output.excel_columns
            print(f"前5列: {columns[:5]}")
            print(f"后10列: {columns[-10:]}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    return True


def test_data_processor_future_years():
    """测试数据处理器对未来年份的支持"""
    print(f"\n=== 测试数据处理器未来年份支持 ===")
    
    # 设置2025年
    config.set_custom_years(["2025"])
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 检查年份字段映射
    print("年份字段映射:")
    for year, fields in processor.year_fields.items():
        print(f"  {year}年: {list(fields.values())}")
    
    # 模拟2025年数据
    mock_api_response = {
        "success": True,
        "data": [
            {
                "majorName": "人工智能",
                "majorPlan": "100",
                "majorReal": "100",
                "majorMaxScore": "700",
                "majorMinScore": "695",
                "majorAvgScore": "698",
                "batch": "2",
                "majorRequirementName": "物理+化学"
            }
        ]
    }
    
    school_info = {
        "collageCode": "10001",
        "collageName": "北京大学",
        "province": "北京",
        "is985": "1",
        "is211": "1"
    }
    
    # 处理2025年数据
    records = processor.process_api_response(mock_api_response, "2025", "物理类", school_info)
    
    if records:
        record = records[0]
        print(f"\n处理后的2025年记录:")
        print(f"  专业名称: {record.get('专业名称')}")
        print(f"  首选科目: {record.get('首选科目')}")
        print(f"  25计划人数: {record.get('25计划人数')}")
        print(f"  25录取最高分: {record.get('25录取最高分')}")
        
        # 验证2025年字段
        expected_2025_fields = ["25计划人数", "25录取人数", "25录取最高分", "25最高分位次", "25录取平均分", "25录取最低分", "25最低分位次"]
        
        for field in expected_2025_fields:
            if field in record and record[field]:
                print(f"  ✅ {field}: {record[field]}")
            else:
                print(f"  ❌ {field}: 缺失或为空")
                return False
        
        print("✅ 2025年数据处理正确")
        return True
    else:
        print("❌ 未获取到处理后的记录")
        return False


def test_merge_future_years():
    """测试未来年份的数据合并"""
    print(f"\n=== 测试未来年份数据合并 ===")
    
    # 设置2024-2026年
    config.set_custom_years(["2024", "2025", "2026"])
    
    processor = DataProcessor()
    
    # 模拟多年数据
    records_by_year = {
        "2026": [
            {
                "学校代码": "10001",
                "学校名称": "北京大学",
                "专业名称": "量子计算",
                "录取批次": "本科批",
                "首选科目": "物理类",
                "26录取最高分": "720",
                "26录取最低分": "715"
            }
        ],
        "2025": [
            {
                "学校代码": "10001",
                "学校名称": "北京大学",
                "专业名称": "量子计算",
                "录取批次": "本科批",
                "首选科目": "物理类",
                "25录取最高分": "710",
                "25录取最低分": "705"
            }
        ],
        "2024": [
            {
                "学校代码": "10001",
                "学校名称": "北京大学",
                "专业名称": "量子计算",
                "录取批次": "本科批",
                "首选科目": "物理类",
                "24录取最高分": "700",
                "24录取最低分": "695"
            }
        ]
    }
    
    # 合并数据
    merged_records = processor.merge_year_data(records_by_year)
    
    if merged_records:
        record = merged_records[0]
        print(f"合并后记录:")
        print(f"  专业名称: {record.get('专业名称')}")
        print(f"  2026年最高分: {record.get('26录取最高分')}")
        print(f"  2025年最高分: {record.get('25录取最高分')}")
        print(f"  2024年最高分: {record.get('24录取最高分')}")
        
        # 验证三年数据都存在
        if (record.get('26录取最高分') == "720" and 
            record.get('25录取最高分') == "710" and 
            record.get('24录取最高分') == "700"):
            print("✅ 未来年份数据合并正确")
            return True
        else:
            print("❌ 数据合并错误")
            return False
    else:
        print("❌ 合并后无记录")
        return False


def test_excel_output_future_years():
    """测试未来年份的Excel输出"""
    print(f"\n=== 测试未来年份Excel输出 ===")
    
    # 设置2025年
    config.set_custom_years(["2025"])
    
    processor = DataProcessor()
    
    # 创建测试记录
    test_record = {
        "省份": "北京",
        "学校代码": "10001",
        "学校名称": "北京大学",
        "专业名称": "未来科技",
        "首选科目": "物理类",
        "25计划人数": "50",
        "25录取人数": "50",
        "25录取最高分": "750",
        "25录取最低分": "745"
    }
    
    # 添加记录并保存
    processor.add_records([test_record])
    filename = processor.save_batch_to_excel("物理类", force_save=True)
    
    if filename:
        print(f"✅ Excel文件已保存: {filename}")
        
        # 验证Excel文件
        try:
            import pandas as pd
            df = pd.read_excel(filename)
            
            print(f"Excel验证: {len(df)} 行, {len(df.columns)} 列")
            
            # 检查2025年字段
            year_2025_fields = [col for col in df.columns if col.startswith('25')]
            print(f"2025年字段: {year_2025_fields}")
            
            if len(year_2025_fields) > 0:
                print("✅ 2025年字段存在于Excel中")
                
                # 检查数据值
                if not df.empty:
                    first_row = df.iloc[0]
                    print(f"2025年最高分: {first_row.get('25录取最高分', 'N/A')}")
                    print("✅ Excel输出测试通过")
                    return True
            else:
                print("❌ 2025年字段缺失")
                return False
                
        except Exception as e:
            print(f"❌ Excel验证失败: {e}")
            return False
    else:
        print("❌ Excel保存失败")
        return False


def main():
    """主函数"""
    print("测试未来年份支持功能\n")
    
    # 测试1：配置功能
    config_test = test_future_years_config()
    
    # 测试2：数据处理器
    processor_test = test_data_processor_future_years()
    
    # 测试3：数据合并
    merge_test = test_merge_future_years()
    
    # 测试4：Excel输出
    excel_test = test_excel_output_future_years()
    
    print(f"\n=== 测试结果总结 ===")
    print(f"配置功能: {'✅ 通过' if config_test else '❌ 失败'}")
    print(f"数据处理器: {'✅ 通过' if processor_test else '❌ 失败'}")
    print(f"数据合并: {'✅ 通过' if merge_test else '❌ 失败'}")
    print(f"Excel输出: {'✅ 通过' if excel_test else '❌ 失败'}")
    
    if config_test and processor_test and merge_test and excel_test:
        print(f"\n🎉 所有测试通过！系统已支持未来年份")
        print(f"\n📋 未来年份使用示例:")
        print(f"python -m spider.main --years 2025                    # 只爬取2025年")
        print(f"python -m spider.main --years 2025,2026               # 爬取2025和2026年")
        print(f"python -m spider.main --years 2024,2025,2026          # 爬取2024-2026年")
        print(f"python -m spider.main --years 2020,2021,2022,2023,2024,2025,2026  # 爬取2020-2026年")
        print(f"\n✨ 系统现在支持任意年份组合，包括过去、现在和未来年份！")
    else:
        print(f"\n❌ 部分测试失败，需要进一步检查")


if __name__ == "__main__":
    main()
